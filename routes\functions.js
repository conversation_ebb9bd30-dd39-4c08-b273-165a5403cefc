const express = require('express');
const router = express.Router();
const multer = require('multer');
const sharp = require('sharp');
const fs = require('fs').promises;
const tempDir = 'resources/temp/';
const imageDir = 'resources/images/';

module.exports = (pool) => {
    const storage = multer.diskStorage({
        destination: function (req, file, cb) {
            cb(null, tempDir);
        },
        filename: function (req, file, cb) {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            cb(null, uniqueSuffix + '-' + file.originalname);
        }
    });

    const upload = multer({ storage: storage });

    const deleteFile = async (filePath) => {
        try {
            await fs.access(filePath, fs.constants.F_OK);
    
            await fs.unlink(filePath);
    
            return { type: 'success', message: 'File deleted successfully' };
        } catch (err) {
            console.error(err);
    
            if (err.code === 'EBUSY' || err.code === 'EEXIST') {
                return { type: 'error', error: 'File is busy or locked, cannot be deleted' };
            }
    
            return { type: 'error', error: 'Failed to delete the file' };
        }
    };
    

    router.post('/upload', upload.single('image'), async (req, res) => {
        try {
            const uploadedFile = req.file;
            const fileName = req.body.fileName;

            if (uploadedFile.mimetype.startsWith('image/')) {
                const webpFileName = fileName + '.webp';

                try {
                    await sharp(uploadedFile.path)
                        .webp()
                        .toFile(imageDir + webpFileName);

                    await deleteFile(uploadedFile.path);

                    return res.status(200).json({ type: 'success', message: 'File uploaded and renamed to .webp' });
                } catch (err) {
                    console.error(err);
                    return res.status(500).json({ type: 'error', error: 'Error processing the file' });
                }
            } else {
                await deleteFile(uploadedFile.path);

                return res.status(400).json({ type: 'error', error: 'Unsupported format!' });
            }
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/list', async (req, res) => {
    try {
        const { category } = req.query;
        const imageDir = 'resources/images/';
        
        let files = [];
        
        if (category && category !== 'all') {
            // Belirli kategori
            const categoryPath = path.join(imageDir, category);
            try {
                const categoryFiles = await fs.readdir(categoryPath);
                files = categoryFiles
                    .filter(file => file.match(/\.(jpg|jpeg|png|gif|webp)$/i))
                    .map(file => ({
                        name: file,
                        path: `resources/images/${category}/${file}`,
                        category: category,
                        url: `/resources/images/${category}/${file}`
                    }));
            } catch (err) {
                // Kategori klasörü yoksa boş array döndür
                files = [];
            }
        } else {
            // Tüm kategoriler
            const categories = [
                'announcements', 'concerts', 'daily_activities', 'franchise',
                'holly_snap', 'holly_snap_posts', 'prizes', 'products', 'shaman', 'users'
            ];
            
            for (const cat of categories) {
                const categoryPath = path.join(imageDir, cat);
                try {
                    const categoryFiles = await fs.readdir(categoryPath);
                    const catFiles = categoryFiles
                        .filter(file => file.match(/\.(jpg|jpeg|png|gif|webp)$/i))
                        .map(file => ({
                            name: file,
                            path: `resources/images/${cat}/${file}`,
                            category: cat,
                            url: `/resources/images/${cat}/${file}`
                        }));
                    files = files.concat(catFiles);
                } catch (err) {
                    // Kategori klasörü yoksa skip et
                    continue;
                }
            }
            
            // Ana dizindeki dosyalar
            try {
                const rootFiles = await fs.readdir(imageDir);
                const rootImageFiles = rootFiles
                    .filter(file => file.match(/\.(jpg|jpeg|png|gif|webp)$/i))
                    .map(file => ({
                        name: file,
                        path: `resources/images/${file}`,
                        category: 'general',
                        url: `/resources/images/${file}`
                    }));
                files = files.concat(rootImageFiles);
            } catch (err) {
                console.error('Error reading root images:', err);
            }
        }
        
        // Dosya detaylarını al
        const filesWithDetails = await Promise.all(
            files.map(async (file) => {
                try {
                    const fullPath = path.join(__dirname, '..', file.path);
                    const stats = await fs.stat(fullPath);
                    return {
                        ...file,
                        size: stats.size,
                        type: `image/${path.extname(file.name).slice(1)}`,
                        createdAt: stats.birthtime.toISOString(),
                        updatedAt: stats.mtime.toISOString()
                    };
                } catch (err) {
                    return {
                        ...file,
                        size: 0,
                        type: 'image/unknown',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };
                }
            })
        );
        
        res.status(200).json({
            type: 'success',
            files: filesWithDetails,
            total: filesWithDetails.length
        });
        
    } catch (err) {
        console.error('Error listing media files:', err);
        res.status(500).json({
            type: 'error',
            message: 'Error listing media files',
            error: err.message
        });
    }
});

    router.post('/delete', async (req, res) => {
        try {
            const fileName = req.body.fileName;

            if (!fileName) {
                return res.status(400).json({
                    type: 'error',
                    message: 'File not found!'
                });
            }

            const result = await deleteFile(imageDir + fileName);

            return res.status(200).json(result);
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                message: 'Internal Server Error'
            });
        }
    });

    return router;
};
