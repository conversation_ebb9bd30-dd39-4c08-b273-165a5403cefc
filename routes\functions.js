const express = require('express');
const router = express.Router();
const multer = require('multer');
const sharp = require('sharp');
const fs = require('fs').promises;
const tempDir = 'resources/temp/';
const imageDir = 'resources/images/';

module.exports = (pool) => {
    const storage = multer.diskStorage({
        destination: function (req, file, cb) {
            cb(null, tempDir);
        },
        filename: function (req, file, cb) {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            cb(null, uniqueSuffix + '-' + file.originalname);
        }
    });

    const upload = multer({ storage: storage });

    const deleteFile = async (filePath) => {
        try {
            await fs.access(filePath, fs.constants.F_OK);
    
            await fs.unlink(filePath);
    
            return { type: 'success', message: 'File deleted successfully' };
        } catch (err) {
            console.error(err);
    
            if (err.code === 'EBUSY' || err.code === 'EEXIST') {
                return { type: 'error', error: 'File is busy or locked, cannot be deleted' };
            }
    
            return { type: 'error', error: 'Failed to delete the file' };
        }
    };
    

    router.post('/upload', upload.single('image'), async (req, res) => {
        try {
            const uploadedFile = req.file;
            const fileName = req.body.fileName;

            if (uploadedFile.mimetype.startsWith('image/')) {
                const webpFileName = fileName + '.webp';

                try {
                    await sharp(uploadedFile.path)
                        .webp()
                        .toFile(imageDir + webpFileName);

                    await deleteFile(uploadedFile.path);

                    return res.status(200).json({ type: 'success', message: 'File uploaded and renamed to .webp' });
                } catch (err) {
                    console.error(err);
                    return res.status(500).json({ type: 'error', error: 'Error processing the file' });
                }
            } else {
                await deleteFile(uploadedFile.path);

                return res.status(400).json({ type: 'error', error: 'Unsupported format!' });
            }
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/delete', async (req, res) => {
        try {
            const fileName = req.body.fileName;

            if (!fileName) {
                return res.status(400).json({
                    type: 'error',
                    message: 'File not found!'
                });
            }

            const result = await deleteFile(imageDir + fileName);

            return res.status(200).json(result);
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                message: 'Internal Server Error'
            });
        }
    });

    return router;
};
