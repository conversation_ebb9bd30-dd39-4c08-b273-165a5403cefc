const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');
const microtime = require('microtime');
const payment = require('../components/payment');
const hollyPoints = require('./hollyPoints');
const dayjs = require('dayjs');

module.exports = (pool) => {
    async function processProducts(userId, orderId, products, merchantOid) {
        for (const product of products) {
            const productId = product.id;
            let price = product.discountPercentage ? product.discountedPrice : product.price;
            let taxExcludingPrice = price; // Initialize taxExcludingPrice

            const model = product.model;
            const quantity = product.quantity;

            if (model == "shop") {
                await pool.query('INSERT INTO order_items SET orderId = ?, quantity = ?, productId = ?, price = ?, selectedOption = ?',
                    [orderId, quantity, productId, price, product.selected]);
            } else {
                for (let i = 0; i < quantity; i++) {
                    let innerQuantity = 1;
                    let bondedTo = null;
                    if (product.type == 1) {
                        innerQuantity = 2;
                        price /= 2;
                        taxExcludingPrice /= 2;
                    }

                    for (let j = 0; j < innerQuantity; j++) {
                        if (bondedTo != null) bondedTo == null;
                        const insertResult = await pool.query('INSERT INTO user_tickets SET userId = ?, ticketId = ?, bondedTo = ?, paidPrice = ?, taxExcludingPrice = ?, qrCode = ?, status = ?, payment = ?, merchantOid = ?',
                            [userId, productId, bondedTo, price, taxExcludingPrice, "qrCode", 0, false, merchantOid]);

                        const insertId = insertResult[0].insertId;
                        if (product.type == 1) bondedTo = insertId;
                        const qrCode = insertId + "HLYSTN" + microtime.now() + "T" + dayjs(product.date).format("DDMMYYYY");

                        await pool.query('UPDATE user_tickets SET qrCode = ? WHERE id = ?', [qrCode, insertId]);
                    }
                }
            }
        }
    }

    router.get('/categories', verifyJWTToken, async (req, res) => {
        try {
            const [categories] = await pool.query('SELECT * FROM shop_categories sc ORDER BY sc.rank');

            if (categories.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Sonuç bulunamadı!' });
            }

            const formattedResults = categories.map(category => ({
                id: category.id,
                name: category.name,
                status: category.status === 1 ? true : false
            }));

            return res.status(200).json({ type: 'success', data: formattedResults });
        } catch (err) {
            console.log(err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }

    });

    router.get('/products', verifyJWTToken, async (req, res) => {

        const userId = req.decodedToken.userId;

        try {
            const [products] = await pool.query('SELECT *, CASE WHEN EXISTS ( SELECT 1 FROM order_items ot JOIN orders o ON o.id = ot.orderId WHERE ot.productId = sp.id AND o.userId = ? ) THEN true ELSE false END AS bought FROM shop_products sp WHERE sp.status = ? ORDER BY sp.rank', [userId, true]);

            if (products.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Sonuç bulunamadı!' });
            }

            const formattedResults = products.map(product => ({
                id: product.id,
                categoryId: product.categoryId,
                name: product.name,
                description: product.description,
                images: product.images.split(','),
                price: product.price,
                discountedPrice: product.discountedPrice,
                discountPercentage: Math.round(((product.price - product.discountedPrice) / product.price) * 100),
                options: product.options,
                hollyPoints: product.hollyPoints,
                status: product.status === 1 ? true : false,
                bought: product.bought === 1 ? true : false
            }));

            return res.status(200).json({ type: 'success', data: formattedResults });
        } catch (err) {
            console.error('Shop products hatası:', err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }

    });

    router.post('/category-products',
        [
            body('categoryId').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;

            const { categoryId } = req.body;

            try {
                const [products] = await pool.query('SELECT *, CASE WHEN EXISTS ( SELECT 1 FROM order_items ot JOIN orders o ON o.id = ot.orderId WHERE ot.productId = sp.id AND o.userId = ? ) THEN true ELSE false END AS bought FROM shop_products sp WHERE sp.status = ? AND sp.categoryId = ? ORDER BY sp.rank', [userId, true, categoryId]);

                if (products.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Sonuç bulunamadı!' });
                }

                const formattedResults = products.map(product => ({
                    id: product.id,
                    categoryId: product.categoryId,
                    name: product.name,
                    description: product.description,
                    images: product.images.split(','),
                    price: product.price,
                    discountedPrice: product.discountedPrice,
                    discountPercentage: Math.round(((product.price - product.discountedPrice) / product.price) * 100),
                    options: product.options,
                    hollyPoints: product.hollyPoints,
                    status: product.status === 1 ? true : false,
                    bought: product.bought === 1 ? true : false
                }));

                return res.status(200).json({ type: 'success', data: formattedResults });
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }

        });

        router.post('/product-details',
            [
                body('productId').notEmpty(),
            ], verifyJWTToken, async (req, res) => {
                // -- VALIDATE -- //
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    const errorMessages = errors.array().map((error) => error.msg);
                    const errorMessageString = errorMessages.join(', ');
                    return res.status(200).json({ type: 'error', error: errorMessageString });
                }
        
                const { productId } = req.body;
        
                try {
                    const [products] = await pool.query('SELECT * FROM shop_products WHERE status = ? AND id = ?', [true, productId]);
        
                    if (!products || products.length === 0) {
                        return res.status(200).json({ type: 'error', error: 'Sonuç bulunamadı!' });
                    }
        
                    const productData = products[0];
        
                    // Eğer `images` null veya undefined ise, boş bir dizi döndür
                    const images = productData.images ? productData.images.split(',') : [];
        
                    const [comments] = await pool.query('SELECT u.firstName, u.lastName, sr.rating, sr.comment FROM shop_ratings sr JOIN users u ON u.id = sr.userId WHERE sr.status = ? AND productId = ?', [true, productId]);
        
                    const product = {
                        id: productData.id,
                        categoryId: productData.categoryId,
                        details: productData.details ? JSON.parse(productData.details) : {},
                        name: productData.name,
                        description: productData.description,
                        images: images, // Boş dizi olarak ayarlandı
                        price: productData.price,
                        discountedPrice: productData.discountedPrice,
                        discountPercentage: productData.discountedPrice
                            ? Math.round(((productData.price - productData.discountedPrice) / productData.price) * 100)
                            : null,
                        options: productData.options ? productData.options.split(',') : [],
                        hollyPoints: productData.hollyPoints,
                        taxRate: productData.taxRate,
                        model: 'shop',
                        avarageRating: comments.length > 0 ? comments.reduce((sum, comment) => sum + comment.rating, 0) / comments.length : 0,
                        comments: comments.length > 0 ? comments : []
                    };
        
                    return res.status(200).json({ type: 'success', data: product });
                } catch (err) {
                    console.error('Product details hatası:', err);
                    // -- HANDLE ERROR -- //
                    res.status(500).json({
                        type: 'error',
                        error: 'Internal Server Error'
                    });
                }
            }
        );
        

    router.post('/product-ratings',
        [
            body('productId').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const { productId } = req.body;

            try {
                const [comments] = await pool.query('SELECT * FROM shop_ratings WHERE status = ? AND productId = ?', [true, productId]);

                if (comments.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Sonuç bulunamadı!' });
                }

                return res.status(200).json({ type: 'success', data: comments });
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }

        });

    router.post('/submit-product-rating',
        [
            body('productId').notEmpty(),
            body('rating').notEmpty(),
            body('comment').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;

            const { productId, comment, rating } = req.body;

            try {
                // -- CHECK ORDER -- //
                const [orders] = await pool.query('SELECT 1 FROM order_items ot JOIN orders o ON o.id = ot.orderId WHERE ot.productId = ? AND o.userId = ?', [productId, userId]);

                if (orders.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Yorum yapmak için önce ürünü almalısınız!' });
                }

                // -- CHECK PAST COMMENTS -- //
                const [comments] = await pool.query('SELECT * FROM shop_ratings WHERE productId = ? AND userId = ?', [productId, userId]);

                if (comments.length > 0) {
                    return res.status(200).json({ type: 'error', error: 'Geçmiş yorumunuz buluyor!' });
                }

                await pool.query('INSERT INTO shop_ratings SET productId = ?, userId = ?, rating = ?, comment = ?',
                    [productId, userId, rating, comment]);

                // -- COMPLETE SUCCESS -- //
                return res.status(200).json({ type: 'success', message: 'Yorumunuz gönderildi!' });
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    router.post('/order',
        [
            body('products').notEmpty(),
            body('addressId').notEmpty(),
            body('billingType').notEmpty(),
            body('ctoken').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;

            const {
                products,
                addressId,
                billingAddressId,
                billingType,
                hollyPoints,
                taxNumber,
                taxOffice,
                companyName,
                efatura,
                ctoken,
                cName,
                cLastFour,
                cSchema
            } = req.body;

            const date = new Date();
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear().toString();
            const dateNumber = `${day}${month}${year}`;

            const user_ip = req.header('x-forwarded-for');
            const merchantOid = "SHPM" + microtime.now();

            var totalPrice = 0;
            var taxExcludingPrice = 0;
            var totalHollyPoints = 0;
            var basket = [];

            products.map((product) => {
                const price = product.discountPercentage ? product.discountedPrice : product.price;
                totalPrice += price * product.quantity;
                taxExcludingPrice += price - (price * (product.taxRate / 100));
                totalHollyPoints += product.hollyPoints ?? 0;
                basket.push([product.name, price, product.quantity])
            })

            try {
                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const user = users[0];

                // -- CHECK ADDRESSES -- //
                const [userAddresses] = await pool.query('SELECT * FROM user_addresses WHERE id = ?', [addressId]);
                if (userAddresses.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Adres bulunamadı!' });
                }
                const userAddress = userAddresses[0]

                // -- GET SETTINGS -- //
                const [settings] = await pool.query('SELECT * FROM settings LIMIT 1');
                const hollyPointsValue = settings[0].hollyPointsValue;

                const hollyPointsToUse = hollyPoints ?? 0;
                hollyDiscount = hollyPointsValue * hollyPointsToUse;
                totalPrice = (totalPrice - hollyDiscount).toFixed(2);
                taxExcludingPrice = (taxExcludingPrice - hollyDiscount).toFixed(2);

                const paymentResult = await payment.getpayment(`${user.firstName} ${user.lastName}`, userAddress.fullAddress, user.phoneNumber, user.email, totalPrice, basket, user_ip, user.utoken, ctoken, merchantOid);
                if (paymentResult.type == "success") {
                    shopfilter = products.filter(item => item.model == "shop");
                    let orderId = null;
                    if (shopfilter.length > 0) {
                        const insertResult = await pool.query('INSERT INTO orders SET userId = ?, addressId = ?, billingAddressId = ?, hollyPoints = ?, price = ?, taxExcludingPrice = ?, billingType = ?, taxNumber = ?, taxOffice = ?, companyName = ?, efatura = ?, merchantOid = ?, status = ?, cToken = ?, cName = ?, cLastFour = ?, cSchema = ?',
                            [userId, addressId, billingAddressId, totalHollyPoints, totalPrice, taxExcludingPrice, billingType, taxNumber, taxOffice, companyName, efatura ?? false, merchantOid, 0, ctoken, cName, cLastFour, cSchema]);

                        orderId = insertResult[0].insertId;
                        const trackingNumber = `${dateNumber}${orderId}`

                        await pool.query('UPDATE orders SET trackingNumber = ? WHERE id = ?',
                            [trackingNumber, orderId]);
                    }

                    processProducts(userId, orderId, products, merchantOid);

                    // -- HANDLE SUCCESS -- //
                    return res.status(200).json({ type: 'success', paymentLink: paymentResult.paymentLink });
                } else {
                    // -- HANDLE PAYMENT ERROR -- //
                    return res.status(200).json({ type: 'error', error: paymentResult.error });
                }
            } catch (err) {
                console.error('Shop order hatası:', err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });


        router.post('/order-without-saved-card',
            [
                body('products').notEmpty(),
                body('addressId').notEmpty(),
                body('billingType').notEmpty(),
                body('card_number').notEmpty(),
                body('expiry_month').notEmpty(),
                body('expiry_year').notEmpty(),
                body('cvv').notEmpty(),
                body('cc_owner').notEmpty(),
            ], verifyJWTToken, async (req, res) => {
                
                // -- VALIDATE -- //
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    const errorMessages = errors.array().map((error) => error.msg);
                    return res.status(400).json({ type: 'error', error: errorMessages.join(', ') });
                }
        
                const userId = req.decodedToken.userId;
        
                const {
                    products,
                    addressId,
                    billingAddressId,
                    billingType,
                    hollyPoints,
                    taxOffice,
                    companyName,
                    efatura,
        
                    // 📌 Kaydedilmemiş kart bilgileri
                    card_number,
                    expiry_month,
                    expiry_year,
                    cvv,
                    cc_owner
                } = req.body;

                const taxNumber = req.body.taxNumber && req.body.taxNumber !== '' ? req.body.taxNumber : null;




                const sanitizedCardNumber = card_number.replace(/\s+/g, "");

        
                const date = new Date();
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear().toString();
                const dateNumber = `${day}${month}${year}`;
        
                const user_ip = req.header('x-forwarded-for') || req.connection.remoteAddress;
                const merchantOid = "SHPM" + microtime.now();
        
                let totalPrice = 0;
                let taxExcludingPrice = 0;
                let totalHollyPoints = 0;
                let basket = [];
        
                products.forEach((product) => {
                    const price = product.discountPercentage ? product.discountedPrice : product.price;
                    totalPrice += price * product.quantity;
                    taxExcludingPrice += price - (price * (product.taxRate / 100));
                    totalHollyPoints += product.hollyPoints ?? 0;
                    basket.push([product.name, price, product.quantity]);
                });
        
                try {
                    // -- CHECK USER -- //
                    const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                    if (users.length === 0) {
                        return res.status(404).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                    }
                    const user = users[0];
        
                    // -- CHECK ADDRESSES -- //
                    const [userAddresses] = await pool.query('SELECT * FROM user_addresses WHERE id = ?', [addressId]);
                    if (userAddresses.length === 0) {
                        return res.status(404).json({ type: 'error', error: 'Adres bulunamadı!' });
                    }
                    const userAddress = userAddresses[0];
        
                    // -- GET SETTINGS -- //
                    const [settings] = await pool.query('SELECT * FROM settings LIMIT 1');
                    const hollyPointsValue = settings[0].hollyPointsValue;
        
                    // -- HOLLY POINTS DISCOUNT -- //
                    const hollyPointsToUse = hollyPoints ?? 0;
                    const hollyDiscount = hollyPointsValue * hollyPointsToUse;
                    totalPrice = Math.round((totalPrice - hollyDiscount) * 100);
                    taxExcludingPrice = Math.round((taxExcludingPrice - hollyDiscount) * 100);
        
                    // 📌 Kaydedilmemiş kart bilgileri ile ödeme işlemi
                    const paymentParams = {
                        user_name: `${user.firstName} ${user.lastName}`,
                        user_address: userAddress.fullAddress,
                        user_phone: user.phoneNumber,
                        email: user.email,
                        payment_amount: totalPrice,
                        basket,
                        user_ip,
                        merchantOid
                    };
        
                    const cardParams = {
                        card_number: sanitizedCardNumber, // 📌 Boşluksuz kart numarası
                        expiry_month,
                        expiry_year,
                        cvv,
                        cc_owner
                    };
        
                    const paymentResult = await payment.processPaymentFlow(paymentParams, cardParams);
        
                    if (paymentResult.type == "success") {
                        shopfilter = products.filter(item => item.model == "shop");
                        let orderId = null;
                        
                        if (shopfilter.length > 0) {
                            const insertResult = await pool.query(
                                'INSERT INTO orders SET userId = ?, addressId = ?, billingAddressId = ?, hollyPoints = ?, price = ?, taxExcludingPrice = ?, billingType = ?, taxNumber = ?, taxOffice = ?, companyName = ?, efatura = ?, merchantOid = ?, status = ?',
                                [
                                    userId, 
                                    addressId, 
                                    billingAddressId, 
                                    totalHollyPoints, 
                                    totalPrice, 
                                    taxExcludingPrice, 
                                    billingType, 
                                    taxNumber, 
                                    taxOffice, 
                                    companyName, 
                                    efatura ?? false, 
                                    merchantOid, 
                                    0,
                                ]
                            );
                    
                            orderId = insertResult[0].insertId;
                            const trackingNumber = `${dateNumber}${orderId}`;
                    
                            await pool.query('UPDATE orders SET trackingNumber = ? WHERE id = ?', [trackingNumber, orderId]);
                        }
                    
                        processProducts(userId, orderId, products, merchantOid);
                    
                        // -- HANDLE SUCCESS -- //
                        return res.status(200).json({ type: 'success', paymentLink: paymentResult.paymentLink });
                    } else {
                        return res.status(400).json({ type: 'error', message: 'Ödeme başarısız.' });
                    }
                    
                } catch (err) {
                    console.error(err);
                    return res.status(500).json({
                        type: 'error',
                        error: 'Internal Server Error'
                    });
                }
            }
        );
        
        

    router.get('/orders', verifyJWTToken, async (req, res) => {

        const userId = req.decodedToken.userId;

        try {
            const [orders] = await pool.query(`
            SELECT
                o.id,
                o.status,
                MAX(sp.name) AS name,
                MAX(sp.images) AS images
            FROM
                orders o
                INNER JOIN order_items oi ON o.id = oi.orderId
                INNER JOIN shop_products sp ON oi.productId = sp.id
            WHERE
                o.userId = 7
            GROUP BY
                o.id, o.status
            ORDER BY
                MAX(oi.createdAt) DESC;
            `, [userId]);
            if (orders.length === 0) {
                return res.status(200).json({ type: 'success', data: [] });
            }

            const formattedResults = orders.map(order => ({
                ...order,
                images: order.images.split(",")
            }));

            return res.status(200).json({ type: 'success', data: formattedResults });
        } catch (err) {
            console.error('Shop orders hatası:', err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }

    });

    router.get('/order-details', verifyJWTToken, async (req, res) => {

        const userId = req.decodedToken.userId;

        const { orderId } = req.query;
        try {
            // -- CHECK ORDERS -- //
            const [orders] = await pool.query('SELECT ua.title as addressTitle, ua.fullAddress, o.cName, o.cLastFour, o.cSchema, o.trackingNumber, o.createdAt, o.status, o.price AS totalPrice, o.carrier, o.deliveryTrackingNumber, o.estimatedDeliveryDate, sp.name AS productName, sp.images AS productImages, sp.id AS productId, sp.createdAt AS productCreatedAt, oi.quantity, oi.selectedOption, oi.price AS itemPrice FROM orders o INNER JOIN order_items oi ON o.id = oi.orderId INNER JOIN shop_products sp ON oi.productId = sp.id INNER JOIN user_addresses ua ON o.addressId = ua.id WHERE o.id = ?', [orderId]);
            if (orders.length === 0) {
                return res.status(200).json({ type: 'success', data: [] });
            }

            const formattedResults = orders.map(order => ({
                name: order.productName,
                quantity: order.quantity,
                selectedOption: order.selectedOption,
                price: order.itemPrice,
                code: `HL${order.productId}T${new Date(order.productCreatedAt).getFullYear().toString().slice(-2)}${String(new Date(order.productCreatedAt).getMonth() + 1).padStart(2, '0')}${String(new Date(order.productCreatedAt).getDate()).padStart(2, '0')}`,
                images: order.productImages.split(",")
            }));

            const results = {
                trackingNumber: orders[0].trackingNumber,
                orderDate: orders[0].createdAt,
                orderStatus: orders[0].status,
                carrier: orders[0].carrier,
                price: orders[0].totalPrice,
                deliveryTrackingNumber: orders[0].deliveryTrackingNumber,
                estimatedDeliveryDate: orders[0].estimatedDeliveryDate,
                cName: orders[0].cName,
                cLastFour: orders[0].cLastFour,
                cSchema: orders[0].cSchema,
                addressTitle: orders[0].addressTitle,
                cityTitle: orders[0].cityTitle,
                districtTitle: orders[0].districtTitle,
                neighborhoodTitle: orders[0].neighborhoodTitle,
                fullAddress: orders[0].fullAddress,
                products: formattedResults
            }

            return res.status(200).json({ type: 'success', data: results });
        } catch (err) {
            console.log(err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }

    });

    return router;
};
