const axios = require('axios');
const xml2js = require('xml2js');

async function sendSms(phoneNumber, message) {
    const key = process.env.SMS_KEY;
    const hash = process.env.SMS_HASH;
    const xmlData = `
      <request>
          <authentication>
              <key>${key}</key>
              <hash>${hash}</hash>
          </authentication>
          <order>
              <sender>HOLLY STONE</sender>
              <sendDateTime></sendDateTime>
              <message>
                  <text>${message}</text>
                  <receipents>
                      <number>${phoneNumber}</number>
                  </receipents>
              </message>
          </order>
      </request>
    `;

    try {
        const response = await axios.post('http://api.iletimerkezi.com/v1/send-sms', xmlData, {
            headers: {
                'Content-Type': 'text/xml',
            },
        });

        const xmlParser = new xml2js.Parser();
        const result = await xmlParser.parseStringPromise(response.data);
        return result;
    } catch (err) {
        console.log(err);
        // -- HANDLE ERROR -- //
        return { type: 'error', error: 'Internal Server Error' };
    }
}

module.exports = sendSms;
