const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');

module.exports = (pool) => {
    router.get('/', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            // -- CHECK USER -- //
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }
            const user = users[0];

            const [earningHistoryHollyTicket] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ?", [1, userId]);
            const [earningHistoryWinWin] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ?", [2, userId]);
            const [earningHistoryHollySnap] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ?", [3, userId]);
            const [earningHistoryHollyShop] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ?", [4, userId]);
            const [earningHistoryCashier] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ?", [5, userId]);
            const [spendHistoryHollyTicket] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_spend_history WHERE spendTo = ? AND userId = ?", [1, userId]);
            const [spendHistoryHollyShop] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_spend_history WHERE spendTo = ? AND userId = ?", [2, userId]);
            const [spendHistoryHollyRealMoney] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_spend_history WHERE spendTo = ? AND userId = ?", [3, userId]);

            formattedResults = {
                hollyPoints: user.hollyPoints,
                earnings: {
                    hollyTicket: earningHistoryHollyTicket[0].total ?? 0,
                    winWin: earningHistoryWinWin[0].total ?? 0,
                    hollySnap: earningHistoryHollySnap[0].total ?? 0,
                    hollyShop: earningHistoryHollyShop[0].total ?? 0,
                    cashier: earningHistoryCashier[0].total ?? 0
                },
                spendings: {
                    hollyTicket: spendHistoryHollyTicket[0].total ?? 0,
                    hollyShop: spendHistoryHollyShop[0].total ?? 0,
                    realMoney: spendHistoryHollyRealMoney[0].total ?? 0
                }
            }

            return res.status(200).json({ type: 'success', data: formattedResults });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/transferHollyPoints',
        [
            body('prizeId').notEmpty().withMessage('Prize ID zorunludur!'),
            body('prizeType').notEmpty().withMessage('Prize Type zorunludur!')
        ], verifyJWTToken, async (req, res) => {

        console.log('transferHollyPoints endpoint çağrıldı');
        console.log('Request body:', req.body);

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map((error) => error.msg);
            console.log('Validasyon hatası:', errorMessages);
            return res.status(200).json({ type: 'error', error: errorMessages.join(', ') });
        }

        const userId = req.decodedToken.userId;
        const { prizeId, prizeType } = req.body;

        console.log('User ID:', userId);
        console.log('Prize ID:', prizeId);
        console.log('Prize Type:', prizeType);

        try {
            console.log('1. Adım: Kullanıcı kontrolü başlıyor');
            // Kullanıcıyı kontrol et
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            console.log('Kullanıcı sorgusu sonucu:', users.length > 0 ? 'Kullanıcı bulundu' : 'Kullanıcı bulunamadı');

            if (users.length === 0) {
                console.log('Hata: Kullanıcı bulunamadı!');
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }

            console.log('2. Adım: Ödül kontrolü başlıyor');

            let prize;
            let pointValue;
            let tableName;

            // Ödül tipine göre farklı tablolarda sorgulama yap
            if (prizeType === 'wheel') {
                // Çark ödülü için prize_wheel_spin_history tablosunu kontrol et
                const [wheelPrizes] = await pool.query('SELECT * FROM prize_wheel_spin_history WHERE id = ? AND userId = ?', [prizeId, userId]);
                console.log('Wheel ödül sorgusu sonucu:', wheelPrizes.length > 0 ? 'Ödül bulundu' : 'Ödül bulunamadı');

                if (wheelPrizes.length === 0) {
                    console.log('Hata: Ödül bulunamadı!');
                    return res.status(200).json({ type: 'error', error: 'Ödül bulunamadı!' });
                }

                prize = wheelPrizes[0];
                tableName = 'prize_wheel_spin_history';
                pointValue = prize.point_value;

                // Ödül daha önce teslim edilmişse hata döndür
                if (prize.delivery === 1) {
                    console.log('Hata: Bu ödül daha önce zaten teslim edilmiş!');
                    return res.status(200).json({ type: 'error', error: 'Bu ödül daha önce zaten teslim edilmiş!' });
                }

                // Eğer point_value null ise, prize JSON'dan değeri çıkarmayı dene
                if (pointValue === null || pointValue === undefined) {
                    console.log('point_value null, JSON içinden değer çıkarılıyor...');
                    try {
                        // prize alanı JSON string olarak saklanıyor olabilir
                        const prizeData = typeof prize.prize === 'string' ? JSON.parse(prize.prize) : prize.prize;
                        console.log('Parse edilen prize JSON:', prizeData);

                        // JSON içinde value alanı varsa
                        if (prizeData && prizeData.value) {
                            pointValue = parseInt(prizeData.value);
                            console.log('JSON value alanından çıkarılan point değeri:', pointValue);
                        }
                        // JSON içinde name alanında "50 Holly Puan" gibi bir değer olabilir
                        else if (prizeData && prizeData.name) {
                            // Örnek: "50 Holly Puan" -> 50 değerini çıkar
                            const match = prizeData.name.match(/\d+/);
                            if (match) {
                                pointValue = parseInt(match[0], 10);
                                console.log('JSON name alanından çıkarılan point değeri:', pointValue);
                            }
                        }

                        // Eğer hala null ise ve type hollyPoints ise
                        if ((pointValue === null || pointValue === undefined) && prizeData && prizeData.type === 'hollyPoints') {
                            // Varsayılan bir değer ata (50 puan)
                            pointValue = 50;
                            console.log('Varsayılan point değeri atandı:', pointValue);
                        }
                    } catch (jsonError) {
                        console.error('JSON parse hatası:', jsonError);
                    }
                }
            }
            else if (prizeType === 'shaman') {
                // Shaman ödülü için shaman_prize tablosunu kontrol et
                const [shamanPrizes] = await pool.query('SELECT * FROM shaman_prize WHERE id = ? AND winnerId = ?', [prizeId, userId]);
                console.log('Shaman ödül sorgusu sonucu:', shamanPrizes.length > 0 ? 'Ödül bulundu' : 'Ödül bulunamadı');

                if (shamanPrizes.length === 0) {
                    console.log('Hata: Ödül bulunamadı!');
                    return res.status(200).json({ type: 'error', error: 'Ödül bulunamadı!' });
                }

                prize = shamanPrizes[0];
                tableName = 'shaman_prize';

                // Ödül daha önce teslim edilmişse hata döndür
                if (prize.delivery === 1) {
                    console.log('Hata: Bu ödül daha önce zaten teslim edilmiş!');
                    return res.status(200).json({ type: 'error', error: 'Bu ödül daha önce zaten teslim edilmiş!' });
                }

                // Shaman ödülü için value alanını kullan
                pointValue = prize.value;
                console.log('Shaman ödülü value değeri:', pointValue);
            }
            else {
                console.log('Hata: Geçersiz ödül tipi!');
                return res.status(200).json({ type: 'error', error: 'Geçersiz ödül tipi!' });
            }

            console.log('Ödül bilgileri:', prize);

            // Hala null ise hata döndür
            if (pointValue === null || pointValue === undefined) {
                console.log('Hata: Point değeri bulunamadı!');
                return res.status(200).json({ type: 'error', error: 'Point değeri bulunamadı! Yönetici ile iletişime geçin.' });
            }

            console.log('Kullanılacak point değeri:', pointValue);

            console.log('3. Adım: Holly Points kazanım geçmişine ekleniyor');
            try {
                // Holly Points kazanım geçmişine ekle
                const [insertResult] = await pool.query(
                    'INSERT INTO holly_points_earning_history (userId, hollyPoints, earnFrom, createdAt, updatedAt, vendorId) VALUES (?, ?, ?, NOW(), NOW(), NULL)',
                    [userId, pointValue, 3]
                );
                console.log('Kazanım geçmişi eklendi, insert ID:', insertResult.insertId);
            } catch (insertErr) {
                console.error('Holly Points kazanım geçmişine ekleme hatası:', insertErr);
                throw new Error(`Kazanım geçmişi eklenirken hata: ${insertErr.message}`);
            }

            console.log('4. Adım: Kullanıcının Holly Points değeri güncelleniyor');
            try {
                // Kullanıcının Holly Points değerini güncelle
                const [updateResult] = await pool.query(
                    'UPDATE users SET hollyPoints = hollyPoints + ? WHERE id = ?',
                    [pointValue, userId]
                );
                console.log('Kullanıcı Holly Points güncellendi, etkilenen satır:', updateResult.affectedRows);
            } catch (updateErr) {
                console.error('Kullanıcı Holly Points güncelleme hatası:', updateErr);
                throw new Error(`Kullanıcı Holly Points güncellenirken hata: ${updateErr.message}`);
            }

            console.log('5. Adım: Ödülün teslim edildiği işaretleniyor');
            try {
                // Ödülün teslim edildiğini işaretle - tabloya göre farklı sorgular
                if (tableName === 'prize_wheel_spin_history') {
                    const [markResult] = await pool.query(
                        'UPDATE prize_wheel_spin_history SET delivery = 1 WHERE id = ? AND userId = ?',
                        [prizeId, userId]
                    );
                    console.log('Ödül teslim edildi olarak işaretlendi, etkilenen satır:', markResult.affectedRows);
                } else if (tableName === 'shaman_prize') {
                    const [markResult] = await pool.query(
                        'UPDATE shaman_prize SET delivery = 1 WHERE id = ? AND winnerId = ?',
                        [prizeId, userId]
                    );
                    console.log('Ödül teslim edildi olarak işaretlendi, etkilenen satır:', markResult.affectedRows);
                }
            } catch (markErr) {
                console.error('Ödül teslim işaretleme hatası:', markErr);
                throw new Error(`Ödül teslim edildi işaretlenirken hata: ${markErr.message}`);
            }

            console.log('İşlem başarıyla tamamlandı');
            return res.status(200).json({ type: 'success', message: 'Holly Points başarıyla aktarıldı ve ödül teslim edildi.' });

        } catch (err) {
            console.error('transferHollyPoints işleminde hata:');
            console.error(err.message);
            console.error(err.stack);
            return res.status(500).json({
                type: 'error',
                error: 'Internal Server Error: ' + err.message
            });
        }
    });


    router.post('/use',
    [
        body('hollyPoints').notEmpty().withMessage('Holly Points değeri zorunludur!'),
    ], verifyJWTToken, async (req, res) => {
        // -- VALIDATE -- //
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map((error) => error.msg);
            const errorMessageString = errorMessages.join(', ');
            return res.status(200).json({ type: 'error', error: errorMessageString });
        }

        const userId = req.decodedToken.userId;
        const { hollyPoints } = req.body;

        try {
            // Kullanıcıyı ve cüzdan bilgisini kontrol et
            const [users] = await pool.query('SELECT id, hollyPoints, walletId FROM users WHERE id = ?', [userId]);

            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }

            const user = users[0];

            // Cüzdan kontrolü
            if (!user.walletId) {
                return res.status(200).json({ type: 'error', error: 'Lütfen cüzdan oluşturun!' });
            }

            // Yeterli Holly Points kontrolü
            if (user.hollyPoints < hollyPoints) {
                return res.status(200).json({ type: 'error', error: 'Yetersiz Holly Points bakiyesi!' });
            }

            // Ayarları getir (Holly Points TL değeri)
            const [settings] = await pool.query('SELECT hollyPointsValue FROM settings LIMIT 1');
            const hollyPointsValue = settings[0].hollyPointsValue || 0;

            // TL değerini hesapla
            const moneyValue = hollyPoints * hollyPointsValue;

            // Veritabanı işlemleri için transaction başlat
            const connection = await pool.getConnection();
            await connection.beginTransaction();

            try {
                // 1. Kullanıcının cüzdanına TL ekle
                await connection.query(
                    'UPDATE wallets SET balance = balance + ? WHERE id = ?',
                    [moneyValue, user.walletId]
                );

                // 2. Kullanıcının Holly Points değerini güncelle
                await connection.query(
                    'UPDATE users SET hollyPoints = hollyPoints - ? WHERE id = ?',
                    [hollyPoints, userId]
                );

                // 3. Holly Points harcama geçmişine ekle
                await connection.query(
                    'INSERT INTO holly_points_spend_history (userId, hollyPoints, spendTo, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
                    [userId, hollyPoints, 3]  // 3 = Real Money olarak kabul edildi
                );

                // 4. Wallet movements tablosuna kayıt ekle
                await connection.query(
                    'INSERT INTO transactions (walletId, type, amount, description, userId, kasaonay, usedhollypoints, cashback) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                    [
                        user.walletId,
                        'holly_points_exchange',  // İşlem tipi
                        moneyValue,               // TL miktarı
                        `${hollyPoints} Holly Points'in TL karşılığı`,  // Açıklama
                        userId,                   // Kullanıcı ID
                        1,                        // Otomatik onaylı (1)
                        hollyPoints,              // Kullanılan holly points
                        0.00                      // Cashback yok
                    ]
                );

                // İşlemleri commit et
                await connection.commit();
                connection.release();

                return res.status(200).json({
                    type: 'success',
                    message: `${hollyPoints} Holly Points başarıyla ${moneyValue} TL'ye dönüştürüldü ve cüzdanınıza eklendi.`
                });

            } catch (error) {
                // Hata durumunda rollback yap
                await connection.rollback();
                connection.release();
                throw error;
            }

        } catch (err) {
            console.error('Holly Points kullanma hatası:', err);
            return res.status(500).json({
                type: 'error',
                error: 'İşlem sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin.'
            });
        }
    });

    // Kullanıcının harcadığı Holly Points geçmişini getir
    router.get('/spending-history', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;

        try {
            // Kullanıcı kontrolü
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }

            // Harcama geçmişini getir
            const [spendingHistory] = await pool.query(
                `SELECT
                    id,
                    hollyPoints,
                    spendTo,
                    createdAt
                FROM holly_points_spend_history
                WHERE userId = ?
                ORDER BY createdAt DESC
                LIMIT 100`,
                [userId]
            );

            // Harcama türlerine göre açıklamaları belirle
            const getSpendingTypeDescription = (spendTo) => {
                switch (spendTo) {
                    case 0:
                        return 'Cüzdan Aktarımı';
                    case 1:
                        return 'Holly Ticket';
                    case 2:
                        return 'Holly Shop';
                    case 3:
                        return 'Nakit Çevirme';
                    case 4:
                        return 'Hediye Kartı';
                    case 5:
                        return 'Çarkıfelek';
                    case 6:
                        return 'Şaman Ödülü';
                    default:
                        return 'Diğer';
                }
            };

            // Verileri formatla
            const formattedHistory = spendingHistory.map(item => ({
                id: item.id,
                amount: item.hollyPoints,
                spendTo: item.spendTo,
                spendType: getSpendingTypeDescription(item.spendTo),
                description: getSpendingTypeDescription(item.spendTo),
                date: item.createdAt
            }));

            // Toplam harcama miktarını hesapla
            const totalSpent = spendingHistory.reduce((total, item) => total + item.hollyPoints, 0);

            // Harcama türlerine göre grupla
            const spendingByType = {};
            spendingHistory.forEach(item => {
                const typeDescription = getSpendingTypeDescription(item.spendTo);
                if (!spendingByType[typeDescription]) {
                    spendingByType[typeDescription] = {
                        total: 0,
                        count: 0
                    };
                }
                spendingByType[typeDescription].total += item.hollyPoints;
                spendingByType[typeDescription].count += 1;
            });

            return res.status(200).json({
                type: 'success',
                data: {
                    totalSpent: totalSpent,
                    spendingHistory: formattedHistory,
                    spendingByType: spendingByType,
                    historyCount: spendingHistory.length
                }
            });

        } catch (err) {
            console.error('Holly Points harcama geçmişi hatası:', err);
            return res.status(500).json({
                type: 'error',
                error: 'Harcama geçmişi alınırken bir hata oluştu.'
            });
        }
    });

    return router;
};
