const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const level = require('../components/level');
const { body, validationResult } = require('express-validator');


module.exports = (pool) => {
    async function calculateWinningPrize(prizes) {
        const availablePrizes = prizes.filter(prize => prize.remaining > 0);

        if (availablePrizes.length === 0) {
            return null;
        }

        const totalPercentage = availablePrizes.reduce((total, prize) => total + parseFloat(prize.percentage), 0);

        const randomNumber = Math.random() * totalPercentage;

        let cumulativePercentage = 0;
        for (const prize of availablePrizes) {
            cumulativePercentage += parseFloat(prize.percentage);

            if (randomNumber < cumulativePercentage) {
                pool.query('UPDATE wheel_prizes SET remaining = remaining - 1 WHERE id = ?', [prize.id]);

                return {
                    id: prize.id,
                    name: prize.name,
                    image: prize.image,
                    type: prize.type,
                    bigPrize: prize.bigPrize,
                    percentage: prize.percentage,
                    remaining: prize.remaining - 1,
                    value: prize.value,
                    sponsorImage: prize.sponsorImage
                };
            }
        }

        return { randomNumber, totalPercentage, cumulativePercentage, availablePrizes };
    }

    router.get('/', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            const [prizes] = await pool.query("SELECT * FROM wheel_prizes");
            if (prizes.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Ödül bulunamadı!' });
            }

            const [spinHistory] = await pool.query(`
            SELECT *
            FROM prize_wheel_spin_history
            WHERE userId = ?
            AND createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY);
            `, [userId]);
            if (spinHistory.length > 0) {
                const lastSpin = spinHistory[0];
                const lastPrize = JSON.parse(lastSpin.prize);
                const lastSpinDate = new Date(lastSpin.createdAt);
                lastSpinDate.setDate(lastSpinDate.getDate() + 7);
                return res.status(200).json({ type: 'error', error: 'Bu hafta zaten ödül aldınız!', date: lastSpinDate, prize: lastPrize });
            }

            return res.status(200).json({ type: 'success', prizes });
        } catch (err) {
            console.error(err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/spin', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            const userLevel = level.findUserLevel(userId, pool);
            const [spinHistory] = await pool.query(`
            SELECT *
            FROM prize_wheel_spin_history
            WHERE userId = ?
            AND createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY);
            `, [userId]);
            if (spinHistory.length > 0) {
                const lastSpin = spinHistory[0];
                const lastPrize = JSON.parse(lastSpin.prize);
                const lastSpinDate = new Date(lastSpin.createdAt);
                lastSpinDate.setDate(lastSpinDate.getDate() + 7);
                return res.status(200).json({ type: 'error', error: 'Bu hafta zaten ödül aldınız!', date: lastSpinDate, prize: lastPrize });
            }

            const [results] = await pool.query('SELECT * FROM wheel_prizes');
            const prizeList = results;

            // Implement your logic to calculate the winning prize based on percentages and remaining count
            const selectedPrize = await calculateWinningPrize(prizeList);

            // Ödülün puan değerini belirle
            let pointValue = null;

            // Eğer ödül tipi hollyPoints ise ve value değeri varsa
            if (selectedPrize && selectedPrize.type === 'hollyPoints' && selectedPrize.value) {
                // Value değeri doğrudan sayı olabilir
                if (typeof selectedPrize.value === 'number') {
                    pointValue = selectedPrize.value;
                }
                // Value değeri string olabilir, sayıya çevirmeyi dene
                else if (typeof selectedPrize.value === 'string' && !isNaN(parseInt(selectedPrize.value))) {
                    pointValue = parseInt(selectedPrize.value);
                }
            }

            // Eğer hala pointValue null ise ve ödül tipi hollyPoints ise, isimden çıkarmayı dene
            if (pointValue === null && selectedPrize && selectedPrize.type === 'hollyPoints' && selectedPrize.name) {
                const match = selectedPrize.name.match(/\d+/);
                if (match) {
                    pointValue = parseInt(match[0], 10);
                }
            }

            console.log('Kazanılan ödül:', selectedPrize);
            console.log('Belirlenen point değeri:', pointValue);

            // point_value sütununu da ekleyerek veritabanına kaydet
            await pool.query(
                'INSERT INTO prize_wheel_spin_history SET userId = ?, prize = ?, point_value = ?',
                [userId, JSON.stringify(selectedPrize), pointValue]
            );

            if (userLevel == 0) {
                level.setLevel(userId, 0, 2, 1, pool);
            }

            return res.status(200).json({ type: 'success', reward: selectedPrize });
        } catch (err) {
            console.error(err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    return router;
};