-- Create table to track scanned QR codes
CREATE TABLE IF NOT EXISTS `scanned_qr_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `qrData` varchar(255) NOT NULL,
  `pointsEarned` int(11) NOT NULL DEFAULT 0,
  `scannedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `userId_qrData` (`userId`, `qrData`),
  KEY `userId` (`userId`),
  KEY `qrData` (`qrData`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
