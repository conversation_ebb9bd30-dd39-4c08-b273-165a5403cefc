const errorHandler = (err, res) => {
    if (err.code === 'ECONNREFUSED') {
        // -- DATABASE CONNECTION ERROR -- //
        res.status(500).json({
            type: 'error',
            error: 'Database Connection Error: Unable to connect to the database.'
        });
    } else {
        // -- OTHER ERRORS -- //
        res.status(500).json({
            type: 'error',
            error: 'Internal Server Error'
        });
    }
}

module.exports = errorHandler;