const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');

require('dotenv').config();

const secretKey = process.env.SECRET_KEY;

module.exports = (pool) => {
    // -- LOGIN -- //
    router.post('/login',
        [
            body('username').notEmpty().withMessage('Kullanıcı adı zorunludur'),
            body('password').notEmpty().withMessage('Şifre zorunludur').isLength({ min: 8 }).withMessage('Şifre en az 8 karakter olmak zorundadır'),
        ],
        async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const { username, password } = req.body;

            try {
                // -- CHECK USER -- //
                const [admins] = await pool.query('SELECT * FROM admins WHERE username = ?', [username]);
                if (admins.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı adı veya şifre hatalı!' });
                }

                // -- CHECK PASSWORD -- //
                const admin = admins[0];
                const isPasswordMatch = await bcrypt.compare(password, admin.password);
                if (!isPasswordMatch) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı adı veya şifre hatalı!' });
                }

                // -- HANDLE SUCCESS -- //
                const token = jwt.sign({ userId: admin.id }, secretKey, { algorithm: 'HS256', expiresIn: '1w' });
                res.status(200).json({ type: 'success', message: 'Giriş başarılı!', token });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- VALIDATE USER -- //
    router.post('/validate', verifyJWTToken,
        async (req, res) => {
            try {
                const userId = req.decodedToken.userId;

                // -- CHECK USER -- //
                const [admins] = await pool.query('SELECT * FROM admins WHERE id = ?', [userId]);
                if (admins.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const admin = admins[0];

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', admin });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    return router;
};