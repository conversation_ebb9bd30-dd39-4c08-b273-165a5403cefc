const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const axios = require('axios'); // Axios ekleyin
require('dotenv').config();


module.exports = (pool) => {

    router.post('/qr-scanner', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;
            const { qrData } = req.body; // QR'dan gelen sipariş numarası

            
            // Sunucunun mevcut tarih ve saati
            const currentDateTime = new Date().toISOString().slice(0, 19).replace('T', ' ');

            
            // Diğer backend'e sipariş numarasını gönder
            const response = await axios.post(process.env.OTHER_BACKEND_URL, { 
                orderId: qrData 
            });

            console.log('response . dara icerigi ',response.data)

            if (response.data && response.data.success && response.data.totalPoints) {
                const earnedPoints = parseInt(response.data.totalPoints);
                
                // Kullanıcı aktivitesini kaydet
                await pool.query('INSERT INTO user_activities (userId) VALUES (?)', [userId]);
                
                // Kullanıcının chatdate ve holly puanlarını güncelle
                await pool.query(
                    'UPDATE users SET chatdate = ?, hollyPoints = hollyPoints + ? WHERE id = ?',
                    [currentDateTime, earnedPoints, userId]
                );
            }
            
            console.log(response)
            // Diğer backend'den gelen cevabı doğrudan döndür
            return res.status(200).json(response.data);
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });


    router.get('/holly-points', verifyJWTToken,
        async (req, res) => {
            try {
                const userId = req.decodedToken.userId;

                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }

                const hollyPoints = users[0].hollyPoints;

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', hollyPoints });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    
    return router;
};
