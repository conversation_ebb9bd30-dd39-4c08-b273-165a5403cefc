const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const axios = require('axios'); // Axios ekleyin
const { EarnFromTypes } = require('./hollyPoints');
require('dotenv').config();


module.exports = (pool) => {

    router.post('/qr-scanner', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;
            const { qrData } = req.body; // QR'dan gelen sipariş numarası

            // Sunucunun mevcut tarih ve saati
            const currentDateTime = new Date().toISOString().slice(0, 19).replace('T', ' ');

            // Önce bu QR kodunun daha önce taranıp taranmadığını kontrol et
            const [existingScans] = await pool.query(
                'SELECT * FROM scanned_qr_codes WHERE userId = ? AND qrData = ?',
                [userId, qrData]
            );

            // Eğer bu QR kodu daha önce taranmışsa, kayıtlı puanları döndür
            if (existingScans && existingScans.length > 0) {
                return res.status(200).json({
                    success: true,
                    message: 'Bu QR kod daha önce taranmıştır!',
                    data: {
                        status: 'already_scanned',
                        message: 'Bu QR kod daha önce taranmıştır!',
                        totalPoints: existingScans[0].pointsEarned,
                        scannedAt: existingScans[0].scannedAt
                    }
                });
            }

            // Diğer backend'e sipariş numarasını gönder
            const response = await axios.post(process.env.OTHER_BACKEND_URL, {
                orderId: qrData
            });

            // Check if totalPoints exists in response.data or in response.data.data
            let earnedPoints = 0;
            if (response.data && response.data.success) {
                if (response.data.totalPoints) {
                    earnedPoints = parseInt(response.data.totalPoints);
                } else if (response.data.data && response.data.data.totalPoints) {
                    earnedPoints = parseInt(response.data.data.totalPoints);
                }

                if (earnedPoints > 0) {


                    try {
                        // QR kodu tarama kaydını ekle
                        const scanResult = await pool.query(
                            'INSERT INTO scanned_qr_codes (userId, qrData, pointsEarned) VALUES (?, ?, ?)',
                            [userId, qrData, earnedPoints]
                        );
                        console.log('QR scan record result:', scanResult);

                        // Kullanıcı aktivitesini kaydet
                        const activityResult = await pool.query('INSERT INTO user_activities (userId) VALUES (?)', [userId]);
                        console.log('Activity insert result:', activityResult);

                        // Kullanıcının chatdate ve holly puanlarını güncelle
                        const updateResult = await pool.query(
                            'UPDATE users SET chatdate = ?, hollyPoints = hollyPoints + ? WHERE id = ?',
                            [currentDateTime, earnedPoints, userId]
                        );
                        console.log('User update result:', updateResult);

                        // Holly points earning history tablosuna ekle (earnFrom = 5 for QR points)
                        const historyResult = await pool.query(
                            'INSERT INTO holly_points_earning_history (userId, hollyPoints, earnFrom) VALUES (?, ?, ?)',
                            [userId, earnedPoints, EarnFromTypes.HOLLY_PAY] // QR için SHAMAN enum değeri kullanılıyor
                        );
                        console.log('History insert result:', historyResult);

                        // Güncel puan bilgisini getir
                        const [userResult] = await pool.query('SELECT hollyPoints FROM users WHERE id = ?', [userId]);
                        console.log('Current user points:', userResult);
                    } catch (dbError) {
                        console.error('Database error when adding points:', dbError);
                    }
                } else {
                    console.log('No points to add - earnedPoints is 0 or not found in response');
                }
            }

            // Diğer backend'den gelen cevabı doğrudan döndür
            return res.status(200).json(response.data);
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });


    router.get('/holly-points', verifyJWTToken,
        async (req, res) => {
            try {
                const userId = req.decodedToken.userId;


                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    console.log(`User ${userId} not found`);
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }

                const hollyPoints = users[0].hollyPoints;
                console.log(`User ${userId} has ${hollyPoints} holly points`);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', hollyPoints });
            } catch (err) {
                console.error('Error getting holly points:', err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });


    return router;
};
