require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const mysql = require('mysql2/promise');
const path = require('path');
const ejsLayouts = require('express-ejs-layouts');
const axios = require('axios');
const { logger, requestLogger } = require('./logger');

const app = express();
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.set('views', path.join(__dirname, '/views'));
app.set('view engine', 'ejs');
app.use(ejsLayouts);
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// İstek loglama middleware'i
app.use(requestLogger);

const PORT = process.env.PORT || 3000;

// —————————————————————————————
// 1) Veritabanı Pool'u
// —————————————————————————————
const pool = mysql.createPool({
  host:     process.env.MYSQL_HOST,     // sewpos-db.uygulama.sewpos.com
  port:     Number(process.env.MYSQL_PORT) || 3306,
  user:     process.env.MYSQL_USER,     // root
  password: process.env.MYSQL_PASSWORD, // Premire07o7@
  database: process.env.MYSQL_DATABASE, // hollystoneapi
  waitForConnections: true,
  connectionLimit: 99,
  queueLimit: 0,
  dateStrings: true,
  ssl: { rejectUnauthorized: false },
  enableKeepAlive: true,
  keepAliveInitialDelay: 10000
});
pool.on('error', (err) => {
  logger.error(`Database pool error: ${err.message}`);
});

// —————————————————————————————
// 2) Sunucuyu Başlat + Socket.io
// —————————————————————————————
const server = app.listen(PORT, () => {
  logger.info(`Server running on http://localhost:${PORT}`);
});

const io = require('socket.io')(server, {
  pingTimeout: 60000,
  cors: { origin: '*' },
});

// —————————————————————————————
// 3) Statik Dosyalar + Route Tanımları
// —————————————————————————————
app.use('/resources', express.static(__dirname + '/resources'));
app.use(express.static('public'));

const mainRoutes = require('./routes/main');
const notificationsRoutes = require('./routes/notifications');
const chatRoutes = require('./routes/chat');
const snapRoutes = require('./routes/snaps');
const hollyPointsRoutes = require('./routes/hollyPoints');
const shamanRoutes = require('./routes/shaman');
const adminRoutes = require('./routes/admin');
const paymentRoutes = require('./routes/payment');
const functionalRoutes = require('./routes/functions');
const shopRoutes = require('./routes/shop');
const usersRoutes = require('./routes/users');
const concertsRoutes = require('./routes/concerts');
const dailyEventsRoutes = require('./routes/dailyEvents');
const wheelRoutes = require('./routes/wheel');
const walletRoutes = require('./routes/wallet');
const qrRoutes = require('./routes/qr');
const cardsRoutes = require('./routes/cards');


app.use('/api', mainRoutes(pool));
app.use('/api/notifications', notificationsRoutes(pool));
app.use('/api/admin', adminRoutes(pool));
app.use('/api/chat', chatRoutes(pool));
app.use('/api/snaps', snapRoutes(pool, io));
app.use('/api/holly-points', hollyPointsRoutes(pool));
app.use('/api/shaman', shamanRoutes(pool));
app.use('/api/payment', paymentRoutes(pool));
app.use('/api/functions', functionalRoutes(pool));
app.use('/api/shop', shopRoutes(pool));
app.use('/api/users', usersRoutes(pool));
app.use('/api/concerts', concertsRoutes(pool));
app.use('/api/daily-events', dailyEventsRoutes(pool));
app.use('/api/wheel', wheelRoutes(pool));
app.use('/api/wallet', walletRoutes(pool));
app.use('/api/qr', qrRoutes(pool));
app.use('/api/cards', cardsRoutes(pool));

// 404 Handler
app.use((req, res) => {
  logger.warn(`404 Not Found: ${req.method} ${req.originalUrl}`);
  res.status(404).send();
});

// —————————————————————————————
// 4) Socket.io Olayları
// —————————————————————————————
// —————————————————————————————
// 4) Socket.io Olayları
// —————————————————————————————
const connectedUsers = {}; // { userId: socketId }
const inappropriateWords = ['badword1', 'badword2', 'badword3'];

const MESSAGE_STATUS = {
  PENDING: 'pending',   // Gönderilmeye çalışılıyor
  SENT: 'sent',         // Sunucuya gönderildi
  DELIVERED: 'delivered', // Karşı tarafa iletildi
  READ: 'read'          // Karşı taraf tarafından okundu
};

// Kullanıcı kendi id'sini söyleyerek bağlanır
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  // a) "user connected" — Kullanıcı online
  socket.on('user connected', (userId) => {
    connectedUsers[userId] = socket.id;
    io.emit('update online users', Object.keys(connectedUsers));
    logger.info(`User connected => ${userId}, ${JSON.stringify(connectedUsers)}`);
  });

  // b) Odaya katıl
  socket.on('join room', (roomId) => {
    socket.join(roomId);
    logger.info(`Socket ${socket.id} joined room ${roomId}`);
  });

  // c) Yazıyor / Durdu event
  socket.on('is_typing', ({ roomId, userId }) => {
    // roomId'yi de gönder
    socket.to(roomId).emit('user_typing', { userId, roomId });
  });

  socket.on('stop_typing', ({ roomId, userId }) => {
    // roomId'yi de gönder
    socket.to(roomId).emit('user_stopped', { userId, roomId });
  });

  // d) Yeni mesaj
// app.js dosyasında socket.io "new message" event handler'ını güncelleyin
socket.on('new message', async (newMessageReceived) => {
  try {
    const { senderId, receiverId, chatRoomId, text, messageStatus = MESSAGE_STATUS.SENT } = newMessageReceived;

    // 1) Uygunsuz kelime filtresi
    const containsInappropriate = inappropriateWords.some((word) =>
      text.toLowerCase().includes(word)
    );
    if (containsInappropriate) {
      return socket.emit('message error', 'Mesaj uygunsuz kelimeler içeriyor!');
    }

    console.log('Processing message:', senderId, receiverId, chatRoomId, text, messageStatus);

    // 2) Yoksa chatRoom oluştur
    const roomId = await createChatRoomIfNotExists(pool, senderId, receiverId, chatRoomId);

    socket.emit('chat_room_created', { roomId, senderId, receiverId });


    // 3) Göndericiyi odaya kat (eğer zaten katılmamışsa)
    socket.join(roomId);
    console.log(`Socket ${socket.id} joined room ${roomId}`);

    // 4) Mesajı veritabanına ekle
    const messageId = await sendMessageToDB(
      pool,
      senderId,
      receiverId,
      roomId, // Oluşturulan veya var olan roomId'yi kullan
      text,
      messageStatus
    );

    const now = new Date();
    const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000));

    // 5) GiftedChat'e uygun payload
    const payload = {
      _id: messageId,
      text: text,
      createdAt: turkeyTime.toISOString(),
      user: {
        _id: senderId,
      },
      chatRoomId: roomId, // Oluşturulan veya var olan roomId'yi kullan
      isDeleted: false,
      messageStatus: messageStatus,
    };

    // 6) Alıcı bağlı değilse bildirim gönder
    if (!connectedUsers[receiverId]) {
      const { receiverDetails, senderDetails } = await fetchNotificationDetails(receiverId, senderId);
      if (receiverDetails && receiverDetails.player_id) {
        const senderFullName = senderDetails
          ? `${senderDetails.firstName} ${senderDetails.lastName}`
          : 'Gönderen Adı';
        await sendNotification(receiverDetails.player_id, senderFullName, text);
      }
    }

    // 7) Bu mesajı odaya yayınla
    io.to(roomId).emit('message received', payload);
    console.log(`Message sent to room ${roomId}`);

  } catch (err) {
    console.error('Error in new message:', err);
    socket.emit('message error', 'Internal Server Error');
  }
});

  // e) Mesaj silme
  socket.on('delete message', async ({ messageId, chatRoomId, userId }) => {
    try {
      // Mesajın senderId'sini veritabanından alıyoruz
      const [rows] = await pool.query(
        `SELECT senderId FROM holly_chat_messages WHERE id=?`,
        [messageId]
      );
      if (rows.length === 0) {
        logger.warn(`Message not found, cannot delete: ${messageId}`);
        return;
      }
      const messageSenderId = rows[0].senderId;

      // Sadece mesajı gönderen kullanıcı kendi mesajını silebilsin
      if (messageSenderId.toString() !== userId.toString()) {
        logger.warn(`User ${userId} is not authorized to delete message ${messageId}`);
        return;
      }

      const success = await deleteMessage(pool, messageId);
      if (success) {
        // Odadaki herkese bildir
        io.to(chatRoomId).emit('message deleted', { messageId, chatRoomId });
      }
    } catch (error) {
      console.error('Error deleting message:', error);
    }
  });

  // f) Mesaj okundu işlemleri
  socket.on('message_read', async ({ messageIds, chatRoomId, receiverId }) => {
    try {
      if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
        logger.warn('No message IDs provided for read status update');
        return;
      }

      logger.info(`Mesaj okundu işlemi: ${messageIds.join(', ')}`);

      // Veritabanında mesajları güncelle - sadece 'read' olmayan mesajlar için
      await pool.query(
        `UPDATE holly_chat_messages SET messageStatus = ?
         WHERE id IN (?) AND messageStatus != ?`,
        [MESSAGE_STATUS.READ, messageIds, MESSAGE_STATUS.READ]
      );

      // Tüm odadaki kullanıcılara bildir
      io.to(chatRoomId).emit('message_status_update', {
        messageIds,
        status: MESSAGE_STATUS.READ,
        chatRoomId
      });

      logger.info(`${messageIds.length} mesaj okundu olarak işaretlendi`);
    } catch (error) {
      logger.error(`Mesaj okundu güncelleme hatası: ${error.message}`);
      logger.error(error.stack);
    }
  });

  // g) Mesaj iletildi işlemleri
  socket.on('message_delivered', async ({ messageId, chatRoomId, receiverId }) => {
    try {
      if (!messageId) {
        logger.warn('No message ID provided for delivered status update');
        return;
      }

      // Mesajın mevcut durumunu kontrol et - 'read' ise güncelleme
      const [statusCheck] = await pool.query(
        `SELECT messageStatus FROM holly_chat_messages WHERE id = ?`,
        [messageId]
      );

      if (statusCheck.length === 0) {
        logger.warn(`Message ${messageId} not found`);
        return;
      }

      // Eğer mesaj zaten 'read' ise, 'delivered' olarak güncelleme
      if (statusCheck[0].messageStatus === MESSAGE_STATUS.READ) {
        logger.info(`Message ${messageId} already read, not downgrading to delivered`);
        return;
      }

      // Veritabanında mesajı güncelle - sadece 'read' olmayan mesajlar için
      await pool.query(
        `UPDATE holly_chat_messages SET messageStatus = ?
         WHERE id = ? AND messageStatus != ?`,
        [MESSAGE_STATUS.DELIVERED, messageId, MESSAGE_STATUS.READ]
      );

      // Gönderene bildir
      io.to(chatRoomId).emit('message_status_update', {
        messageId,
        status: MESSAGE_STATUS.DELIVERED,
        chatRoomId
      });

      logger.info(`Mesaj ${messageId} iletildi olarak işaretlendi`);
    } catch (error) {
      logger.error(`Mesaj iletildi güncelleme hatası: ${error.message}`);
      logger.error(error.stack);
    }
  });

  // h) Socket disconnect — Kullanıcı offline
  socket.on('disconnect', () => {
    for (const userId in connectedUsers) {
      if (connectedUsers[userId] === socket.id) {
        delete connectedUsers[userId];
        break;
      }
    }
    io.emit('update online users', Object.keys(connectedUsers));
    logger.info(`Socket disconnected => ${socket.id}`);
  });
});

// —————————————————————————————————————————————
// 5) Veritabanına Mesaj Yazma/Silme Fonksiyonları
// —————————————————————————————————————————————
async function sendMessageToDB(pool, senderId, receiverId, chatRoomId, text, messageStatus) {
  try {
    // (1) Yoksa chatRoom oluştur
    const roomId = await createChatRoomIfNotExists(pool, senderId, receiverId, chatRoomId);

    // (2) Mesaj ekle
    const [result] = await pool.query(
      `INSERT INTO holly_chat_messages
       (senderId, receiverId, chatRoomId, messageContent, messageStatus, createdAt)
       VALUES (?, ?, ?, ?, ?, CONVERT_TZ(NOW(), '+00:00', '+03:00'))`,
      [senderId, receiverId, roomId, text, messageStatus]
    );
    const insertedId = result.insertId;

    // (3) holly_chat_rooms tablosunun updatedAt alanını güncelle - yine Türkiye saati ile
    await pool.query(
      `UPDATE holly_chat_rooms
       SET updatedAt = CONVERT_TZ(NOW(), '+00:00', '+03:00')
       WHERE id = ?`,
      [roomId]
    );

    return insertedId;
  } catch (err) {
    logger.error(`Error inserting message: ${err.message}`);
    logger.error(err.stack);
    throw err;
  }
}

// app.js dosyasında createChatRoomIfNotExists fonksiyonunu güncelleyin
async function createChatRoomIfNotExists(pool, senderId, receiverId, chatRoomId) {
  // Front-end chatRoomId gönderiyorsa DB'de var kabul ediyoruz
  if (chatRoomId) {
    return chatRoomId;
  }
  try {
    // Var mı diye kontrol
    const [rows] = await pool.query(
      `SELECT id FROM holly_chat_rooms
       WHERE (senderId=? AND receiverId=?) OR (senderId=? AND receiverId=?)`,
      [senderId, receiverId, receiverId, senderId]
    );
    if (rows.length > 0) {
      return rows[0].id;
    }

    // Yoksa oluştur
    const [insertResult] = await pool.query(
      `INSERT INTO holly_chat_rooms (senderId, receiverId, createdAt, updatedAt)
       VALUES (?, ?, NOW(), NOW())`,
      [senderId, receiverId]
    );

    const newRoomId = insertResult.insertId;

    // ÖNEMLİ: Yeni oda oluşturulduğunda, göndericiyi otomatik olarak odaya kat
    const senderSocketId = connectedUsers[senderId];
    if (senderSocketId) {
      const senderSocket = io.sockets.sockets.get(senderSocketId);
      if (senderSocket) {
        senderSocket.join(newRoomId);
        console.log(`Sender ${senderId} joined new room ${newRoomId}`);
      }
    }

    // Alıcı bağlı ise, onu da odaya kat
    const receiverSocketId = connectedUsers[receiverId];
    if (receiverSocketId) {
      const receiverSocket = io.sockets.sockets.get(receiverSocketId);
      if (receiverSocket) {
        receiverSocket.join(newRoomId);
        console.log(`Receiver ${receiverId} joined new room ${newRoomId}`);
      }
    }

    return newRoomId;
  } catch (err) {
    console.error('Error creating chat room:', err);
    throw err;
  }
}

async function deleteMessage(pool, messageId) {
  try {
    const [rows] = await pool.query(
      `SELECT id FROM holly_chat_messages WHERE id=?`,
      [messageId]
    );
    if (rows.length === 0) {
      logger.warn(`Message not found, cannot delete: ${messageId}`);
      return false;
    }
    await pool.query(
      `UPDATE holly_chat_messages
       SET senderDeleted = 1
       WHERE id = ?`,
      [messageId]
    );
    return true;
  } catch (err) {
    logger.error(`Error deleting message: ${err.message}`);
    logger.error(err.stack);
    return false;
  }
}

async function fetchNotificationDetails(receiverId, senderId) {
  // Tek sorguda hem receiver hem de sender bilgilerini alıyoruz.
  const [rows] = await pool.query(
    'SELECT id, player_id, firstName, lastName FROM users WHERE id IN (?, ?)',
    [receiverId, senderId]
  );

  let receiverDetails = null;
  let senderDetails = null;

  rows.forEach(row => {
    if (row.id.toString() === receiverId.toString()) {
      receiverDetails = row;
    }
    if (row.id.toString() === senderId.toString()) {
      senderDetails = row;
    }
  });

  return { receiverDetails, senderDetails };
}



async function sendNotification(playerId, senderName, content) {
  // Hangi player_id'ye bildirim gönderileceğini log'luyoruz:

  // Log notification details
  logger.info(`Sending notification to player ID: ${playerId}, from: ${senderName}, content: ${content}`);

  const headers = {
    "Content-Type": "application/json; charset=utf-8",
    "Authorization": `Basic ${process.env.ONESIGNAL_API_KEY}`
  };

  const data = {
    app_id: process.env.ONESIGNAL_APP_ID,
    include_player_ids: [playerId.toString()],
    headings: { "en": `${senderName}, sana mesaj gönderdi` },
    contents: { "en": `${content}` },
  };

  try {
    const response = await axios.post('https://onesignal.com/api/v1/notifications', data, { headers });
    logger.info(`Notification sent: ${JSON.stringify(response.data)}`);
    return true;
  } catch (error) {
    logger.error(`Error sending notification: ${error.response ? JSON.stringify(error.response.data) : error.message}`);
    logger.error(error.stack);
    return false;
  }
}

