const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const verifyJWTToken = require('../middleware/verifyJWTToken');
const sendSms = require('../components/sendSms');
const sendMailOtp = require('../components/sendMailOtp');

const levelComponent = require('../components/level');
const moment = require('moment');
const { body, validationResult } = require('express-validator');
const dayjs = require('dayjs');
const multer = require('multer');
const sharp = require('sharp');
const winston = require('winston');
const fs = require('fs').promises;

const imageDir = 'resources/images/users/';
const tempDir = 'resources/temp/';

require('dotenv').config();

const logger = winston.createLogger({
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/error.log' })
    ]
});

const secretKey = process.env.SECRET_KEY;

module.exports = (pool) => {

    const generateRandomReferralCode = (length) => {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let code = '';
        for (let i = 0; i < length; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            code += characters.charAt(randomIndex);
        }
        return code;
    };

    const generateRandomFileName = () => {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let code = '';
        for (let i = 0; i < 10; i++) { // Örnek olarak 10 karakterlik bir rastgele dosya adı oluşturuyoruz
            const randomIndex = Math.floor(Math.random() * characters.length);
            code += characters.charAt(randomIndex);
        }
        return code;
    };


    const storage = multer.diskStorage({
        destination: function (req, file, cb) {
            cb(null, tempDir);
        },
        filename: function (req, file, cb) {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const extension = file.originalname.split('.').pop(); // Dosya uzantısını al
            cb(null, uniqueSuffix + '-' + generateRandomFileName() + '.' + extension);
        }
    });


    const deleteFile = async (filePath) => {
        try {
            await fs.access(filePath, fs.constants.F_OK);

            await fs.unlink(filePath);

            return { type: 'success', message: 'File deleted successfully' };
        } catch (err) {
            console.error(err);

            if (err.code === 'EBUSY' || err.code === 'EEXIST') {
                return { type: 'error', error: 'File is busy or locked, cannot be deleted' };
            }

            return { type: 'error', error: 'Failed to delete the file' };
        }
    };

    const upload = multer({ storage: storage });

    router.post('/save-push-id', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;  // JWT'den kullanıcı ID'sini al
            const { oneSignalId } = req.body;  // İstekten pushId'yi al


            // Kullanıcıyı güncelle ve push_id'yi kaydet
            await pool.query('UPDATE users SET player_id = ? WHERE id = ?', [oneSignalId, userId]);

            return res.status(200).json({ type: 'success', message: 'Push ID başarıyla kaydedildi' });
        } catch (err) {
            console.error('Push ID kaydedilirken bir hata oluştu:', err);
            res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
    });


    router.post('/profile-pic', upload.single('image'), verifyJWTToken, async (req, res) => {
        try {
            const uploadedFile = req.file;
            const userId = req.decodedToken.userId;

            if (uploadedFile.mimetype.startsWith('image/')) {
                const webpFileName = generateRandomFileName() + '.webp';

                try {
                    await sharp(uploadedFile.path)
                        .webp()
                        .toFile(imageDir + webpFileName);

                    await deleteFile(uploadedFile.path);

                    await pool.query('UPDATE users SET image = CONCAT("users/", ?) WHERE id = ?', [webpFileName, userId]);

                    return res.status(200).json({ type: 'success', message: 'File uploaded and renamed to .webp' });
                } catch (err) {
                    console.error(err);
                    return res.status(500).json({ type: 'error', error: 'Error processing the file' });
                }
            } else {
                await deleteFile(uploadedFile.path);
                return res.status(400).json({ type: 'error', error: 'Unsupported format!' });
            }
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/cover-pic', upload.single('image'), verifyJWTToken, async (req, res) => {
        try {
            const uploadedFile = req.file;
            const userId = req.decodedToken.userId;

            if (uploadedFile.mimetype.startsWith('image/')) {
                const webpFileName = generateRandomFileName() + '.webp'; // Yeni dosya adı oluşturuluyor

                try {
                    await sharp(uploadedFile.path)
                        .webp()
                        .toFile(imageDir + webpFileName);

                    await deleteFile(uploadedFile.path);

                    await pool.query('UPDATE users SET cover = CONCAT("users/", ?) WHERE id = ?', [webpFileName, userId]);

                    return res.status(200).json({ type: 'success', message: 'File uploaded and renamed to .webp' });
                } catch (err) {
                    console.error(err);
                    return res.status(500).json({ type: 'error', error: 'Error processing the file' });
                }
            } else {
                await deleteFile(uploadedFile.path);
                return res.status(400).json({ type: 'error', error: 'Unsupported format!' });
            }
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });


    // -- SET USER MOOD -- //
    router.post('/set-mood', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;
            const { mood } = req.body;

            // Kullanıcıyı güncelle
            await pool.query('UPDATE users SET mood = ? WHERE id = ?', [mood, userId]);

            return res.status(200).json({ type: 'success', message: 'Mood durumu güncellendi' });
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });


    // -- SEND GIFT CARD -- //
    router.post('/send-gift-card',
        [
            body('referralCode').notEmpty().withMessage('Referans kodu zorunludur'),
            body('cardId').notEmpty().withMessage('Kart id zorunludur'),
        ], verifyJWTToken,
        async (req, res) => {
            try {
                // -- VALIDATE -- //
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    const errorMessages = errors.array().map((error) => error.msg);
                    const errorMessageString = errorMessages.join(', ');
                    return res.status(200).json({ type: 'error', error: errorMessageString });
                }

                const userId = req.decodedToken.userId;

                const { referralCode, cardId } = req.body;

                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length == 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const user = users[0];

                // -- CHECK FRIEND -- //
                const [friends] = await pool.query('SELECT * FROM users WHERE referralCode = ?', [referralCode]);
                if (friends.length == 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const friend = friends[0];

                // -- SEND CARD -- //
                await pool.query('UPDATE gift_cards SET userId = ? WHERE id = ?',
                    [friend.id, cardId]);

                // -- LOG THE SENDING -- //
                await pool.query('INSERT INTO gift_history SET cardId = ?, senderId = ?, receiverId = ?',
                    [cardId, user.id, friend.id]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success' });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- KAZANDIKLARIM -- //
    router.get('/kazandiklarim', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;

            // prize_wheel_spin_history tablosundan prize, delivery, createdAt ve qrCode alanlarını seçerek sorguyu gerçekleştirin
            const [shamanPrizeData] = await pool.query('SELECT prize, id, prizeImage, prizeType, value, prizeSponsorImage, delivery, endDate, updatedAt FROM shaman_prize WHERE winnerId = ?', [userId]);

            const [spinHistory] = await pool.query('SELECT prize, id, delivery, createdAt, qrCode FROM prize_wheel_spin_history WHERE userId = ?', [userId]);

            // spinHistory'deki prize alanını JSON formatından dönüştürün
            const formattedSpinHistory = spinHistory.map(history => {
                const formattedPrize = JSON.parse(history.prize);
                return {
                    ...formattedPrize,
                    delivery: history.delivery,
                    endDate: history.createdAt,
                    id: history.id,
                    qrCode: history.qrCode, // qrCode alanını ekleyin
                    odul: "spinHistory" // odul alanını ekleyin ve değeri "spinHistory" olarak ayarlayın
                };
            });

            // shamanPrizeData için prizeType'i type olarak çevir
            const formattedShamanPrizeData = shamanPrizeData.map(data => ({
                ...data,
                type: data.prizeType, // prizeType alanını type olarak ekle
                odul: "shamanPrizeData" // odul alanını ekle
            }));

            // Kombinlenmiş veriyi oluşturun
            const combinedData = {
                kazandiklarim: [
                    ...formattedShamanPrizeData,
                    ...formattedSpinHistory
                ]
            };

            res.status(200).json({
                type: 'success',
                data: combinedData
            });
        } catch (err) {
            console.log(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/check-referral', verifyJWTToken, async (req, res) => {
        const { referralCode } = req.body;
        if (!referralCode) return res.status(200).json({ type:'error', error:'Referans kodu zorunludur' });

        const [users] = await pool.query('SELECT id FROM users WHERE referralCode = ?', [referralCode]);
        if (users.length === 0) return res.status(200).json({ type:'error', error:'Kullanıcı bulunamadı' });

        return res.status(200).json({ type:'success' });
      });

    // Kart detayları ve işlem geçmişi endpoint'i (Frontend için)
    router.get("/card-details/:cardNumber", verifyJWTToken, async (req, res) => {
        try {
            const { cardNumber } = req.params;
            logger.info(`Kart detayları isteniyor: ${cardNumber}`);

            // Gift Card kontrolü (cardNumber, code alanına karşılık geliyor)
            const [cardData] = await pool.query('SELECT * FROM gift_cards WHERE code = ?', [cardNumber]);

            if (cardData.length === 0) {
                logger.warn(`Kart bulunamadı: ${cardNumber}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Kart bulunamadı.'
                });
            }
            const card = cardData[0];
            logger.info(`Kart bulundu: ${card.id}, bakiye: ${card.value}`);

            // Kart işlem geçmişini al
            const [rawTransactionsData] = await pool.query(
                `SELECT
                    id,
                    amount,
                    createdAt as date,
                    CASE
                        WHEN amount > 0 THEN 'deposit'
                        ELSE 'payment'
                    END as type,
                    CASE
                        WHEN amount > 0 THEN 'Kart Yükleme'
                        WHEN amount < -40 THEN 'Kahve Siparişi'
                        WHEN amount < -30 THEN 'İçecek Siparişi'
                        WHEN amount < -20 THEN 'Tatlı Siparişi'
                        ELSE 'Ürün Siparişi'
                    END as description
                FROM gift_card_usage_history
                WHERE cardId = ?
                ORDER BY createdAt DESC
                LIMIT 50`,
                [card.id]
            );

            // amount değerini number tipine dönüştür
            const transactionsData = rawTransactionsData.map(transaction => ({
                ...transaction,
                amount: parseFloat(transaction.amount) // String'den number'a dönüştür
            }));

            logger.info(`İşlem geçmişi: ${transactionsData.length} işlem bulundu`);

            // Cache kontrolü için headers
            res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
            res.setHeader('Surrogate-Control', 'no-store');

            // Başarılı yanıt dön
            return res.status(200).json({
                type: 'success',
                balance: parseFloat(card.value),
                transactions: transactionsData
            });

        } catch (error) {
            logger.error(`Kart detayları getirme hatası: ${error.message}`);
            return res.status(200).json({
                type: 'error',
                error: 'Kart detayları alınırken bir hata oluştu.'
            });
        }
    });

      // — SEND TICKET AS GIFT —
      router.post('/send-ticket-gift', verifyJWTToken, async (req, res) => {
        const { referralCode, ticketId } = req.body;
        if (!referralCode || !ticketId) {
          return res.status(200).json({ type:'error', error:'ReferralCode ve ticketId zorunludur' });
        }

        // Validate recipient
        const [friends] = await pool.query('SELECT id FROM users WHERE referralCode = ?', [referralCode]);
        if (friends.length === 0) return res.status(200).json({ type:'error', error:'Geçersiz referans kodu' });
        const receiverId = friends[0].id;

        // Validate ticket ownership + status
        const [tickets] = await pool.query('SELECT userId, giftStatus FROM user_tickets WHERE id = ?', [ticketId]);
        if (tickets.length === 0) return res.status(200).json({ type:'error', error:'Bilet bulunamadı' });
        const ticket = tickets[0];
        if (ticket.userId !== req.decodedToken.userId) {
          return res.status(200).json({ type:'error', error:'Bu bilet size ait değil' });
        }
        if (ticket.giftStatus && ticket.giftStatus !== 'available') {
          return res.status(200).json({ type:'error', error:'Bu bilet zaten hediye edilmiş' });
        }

        // Update giftStatus + bondedTo (receiver)
        await pool.query(
          'UPDATE user_tickets SET giftStatus = ?, bondedTo = ? WHERE id = ?',
          ['sent', receiverId, ticketId]
        );

        return res.status(200).json({ type:'success', message:'Bilet başarıyla gönderildi' });
      });

    // — İADE TALEBİ OLUŞTUR —
    router.post('/refund-ticket', verifyJWTToken, async (req, res) => {
        try {
          const userId = req.decodedToken.userId;
          const { ticketId } = req.body;
          if (!ticketId) return res.status(200).json({ type:'error', error:'ticketId zorunludur' });

          const [rows] = await pool.query('SELECT userId, status FROM user_tickets WHERE id = ?', [ticketId]);
          if (rows.length === 0) return res.status(200).json({ type:'error', error:'Bilet bulunamadı' });

          const ticket = rows[0];
          if (ticket.userId !== userId) return res.status(200).json({ type:'error', error:'Bu bilet size ait değil' });

          if (ticket.status === 'iade-bekliyor') return res.status(200).json({ type:'error', error:'İade talebi zaten oluşturulmuş' });
          if (ticket.status.startsWith('iade')) return res.status(200).json({ type:'error', error:'Bu bilet zaten iade edilmiş' });

          await pool.query('UPDATE user_tickets SET status = ? WHERE id = ?', ['iade-bekliyor', ticketId]);
          return res.status(200).json({ type:'success', message:'İade talebi başarıyla oluşturuldu' });
        } catch (err) {
          console.error(err);
          return res.status(500).json({ type:'error', error:'Internal Server Error' });
        }
      });



    // -- GET USER DATA -- //
    router.get('/profile', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;

            const [users] = await pool.query("SELECT u.*, c.title as city FROM users u INNER JOIN cities c ON c.id = u.cityId WHERE u.id = ?", [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }

            const formattedUsers = users.map(async user => ({
                ...user,
                level: await levelComponent.findUserLevel(user.id, pool)
            }));

            const userList = await Promise.all(formattedUsers);
            const user = userList[0];

            const [userActivities] = await pool.query(
                `SELECT * FROM user_activities
                WHERE userId = ?
                AND MONTH(createdAt) = MONTH(CURRENT_DATE())
                AND YEAR(createdAt) = YEAR(CURRENT_DATE())
                ORDER BY createdAt DESC
                LIMIT 15`,
                [userId]
            );
           const formattedActivities = userActivities.map(activity => ({
                ...activity,
                dayNumber: parseInt(dayjs(activity.createdAt).format('DD'))
            }))

            const [leaderBoard] = await pool.query(`
                WITH earnings AS (
                    SELECT e.userId, SUM(e.hollyPoints) AS total_earnings
                    FROM holly_points_earning_history e
                    GROUP BY e.userId
                ), spending AS (
                    SELECT s.userId, SUM(s.hollyPoints) AS total_spending
                    FROM holly_points_spend_history s
                    GROUP BY s.userId
                )
                SELECT
                  e.userId,
                  CONCAT(u.firstName, ' ', u.lastName) AS nameLastName,
                  u.levelNumber AS level,
                  u.image,
                  u.status,
                  COALESCE(e.total_earnings, 0) - COALESCE(s.total_spending, 0) AS hollyPoints
                FROM earnings e
                JOIN users u ON e.userId = u.id
                LEFT JOIN spending s ON e.userId = s.userId
                ORDER BY hollyPoints DESC;
              `);


// Bulunduğu index (–1 ise listede yok)
const index = leaderBoard.findIndex(item => item.userId === userId);

// Mevcut kullanıcı objesini hazırla
let current;
if (index !== -1) {
  current = leaderBoard[index];
} else {
  current = {
    userId,
    nameLastName: `${user.firstName} ${user.lastName}`,
    level: user.level,
    image: user.image,
    status: user.status,
    hollyPoints: 0
  };
}

// Eğer kullanıcı listede yoksa, eklenmesi gereken pozisyonu hesapla
let prev = {};
let next = {};
if (index !== -1) {
  if (index > 0) prev = leaderBoard[index - 1];
  if (index < leaderBoard.length - 1) next = leaderBoard[index + 1];
} else {
  // 0 puanlı kullanıcı listede olsaydı nereye girerdi?
  const position = leaderBoard.findIndex(item => item.hollyPoints <= 0);
  const insertPos = position === -1 ? leaderBoard.length : position;
  if (insertPos > 0) prev = leaderBoard[insertPos - 1];
  if (insertPos < leaderBoard.length) next = leaderBoard[insertPos];
}

const shamanList = [current, prev, next];

const [activeTickets] = await pool.query(`
    SELECT
      ut.*,
      c.name AS concertName,
      c.image AS concertImage,
      c.date AS concertDate,
      c.gatedate AS gatedate,
      v.name AS vendorName,
      v.latitude,
      v.longitude,
      ct.is_refundable AS refundable
    FROM user_tickets ut
    JOIN concert_tickets ct ON ct.id = ut.ticketId
    JOIN concerts c ON c.id = ct.concertId
    JOIN vendors v ON c.vendorId = v.id
    WHERE ut.userId = ?
      AND ut.payment = TRUE
      AND ut.status IN ('aktif','iade-bekliyor')
    ORDER BY ut.createdAt DESC
  `, [userId]);


  const formattedActiveTickets = activeTickets.map(ticket => ({
    ...ticket,
    city: {
      name: ticket.vendorName,
      lat: ticket.latitude,
      long: ticket.longitude,
    },
    refundable: ticket.refundable    // boolean olarak dönecek
  }));



  // — ONLY USED (KULLANILDI)
const [usedTickets] = await pool.query(`
    SELECT
      ut.*,
      c.name AS concertName,
      c.image AS concertImage,
      c.date AS concertDate
    FROM user_tickets ut
    JOIN concert_tickets ct ON ct.id = ut.ticketId
    JOIN concerts c ON c.id = ct.concertId
    WHERE ut.userId = ?
      AND ut.payment = TRUE
      AND ut.status = 'kullanildi'
    ORDER BY ut.createdAt DESC
    LIMIT 5
  `, [userId]);



            const [giftCards] = await pool.query('SELECT gc.*, u.firstName, u.lastName FROM gift_cards gc INNER JOIN users u ON u.id = gc.userId WHERE gc.userId = ? AND gc.cityId = ? AND gc.status = ?', [userId, user.cityId, true]);

            return res.status(200).json({ type: 'success', user, userActivities: formattedActivities, activeTickets: formattedActiveTickets, usedTickets, shamanList, giftCards });
        } catch (err) {
            // -- HANDLE ERROR -- //
            console.log(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    // -- UPDATE PASSWORD -- //
    router.post('/update-password',
        [
            body('currentPassword')
                .notEmpty().withMessage('Mevcut şifre zorunludur')
                .isLength({ min: 6 }).withMessage('Mevcut şifre en az 6 karakter olmak zorundadır')
                .isLength({ max: 64 }).withMessage('Mevcut şifre en fazla 64 karakter olmak zorundadır'),
            body('newPassword')
                .notEmpty().withMessage('Yeni şifre zorunludur')
                .isLength({ min: 6 }).withMessage('Yeni şifre en az 6 karakter olmak zorundadır')
                .isLength({ max: 64 }).withMessage('Yeni şifre en fazla 64 karakter olmak zorundadır')
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;

            const { currentPassword, newPassword } = req.body;

            try {
                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }

                bcrypt.compare(currentPassword, users[0].password, async (err, result) => {
                    if (err) {
                        return res.status(500).json({ type: 'error', error: 'Internal Server Error' });
                    }
                    if (result) {
                        // -- HASH THE PASSWORD -- //
                        const hashedPassword = await bcrypt.hash(newPassword, 10);

                        // -- UPDATE PASSWORD -- //
                        await pool.query(
                            'UPDATE users SET password = ?',
                            [hashedPassword]
                        );

                        // -- HANDLE SUCCESS -- //
                        res.status(200).json({ type: 'success', message: 'Güncelleme başarılı!' });
                    } else {
                        return res.status(200).json({ type: 'error', error: 'Mevcut şifre hatalı!' });
                    }
                });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

        router.post('/update-city', verifyJWTToken, async (req, res) => {
            try {
                const userId = req.decodedToken.userId;

                const { cityId } = req.body;

                // Eksik değer kontrolü
                if (!cityId) {
                    return res.status(400).json({ type: 'error', error: 'cityId eksik!' });
                }

                // Kullanıcı kontrolü
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(404).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }

                // Kullanıcı güncelleme
                await pool.query('UPDATE users SET cityId = ? WHERE id = ?', [cityId, userId]);

                // Başarılı cevap
                res.status(200).json({ type: 'success' });
            } catch (err) {
                console.error('Hata:', err); // Hatanın loglanması
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error',
                });
            }
        });


    // -- UPDATE USER -- //
    router.post('/profile',
        [
            body('firstName').notEmpty().withMessage('Başlık zorunludur'),
            body('lastName').notEmpty().withMessage('Şehir zorunludur'),
            body('email').notEmpty().withMessage('İlçe zorunludur'),
        ], verifyJWTToken,
        async (req, res) => {
            try {
                // -- VALIDATE -- //
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    const errorMessages = errors.array().map((error) => error.msg);
                    const errorMessageString = errorMessages.join(', ');
                    return res.status(200).json({ type: 'error', error: errorMessageString });
                }

                const userId = req.decodedToken.userId;

                const { firstName, lastName, email } = req.body;

                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length == 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }

                // -- UPDATE USER -- //
                await pool.query('UPDATE users SET firstName = ?, lastName = ?, email = ? WHERE id = ?',
                    [firstName, lastName, email, userId]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success' });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- GET ADDRESSES -- //
    router.get('/addresses', verifyJWTToken,
        async (req, res) => {
            try {
                const userId = req.decodedToken.userId;

                // -- CHECK ADDRESSES -- //
                const [userAddresses] = await pool.query('SELECT * FROM user_addresses WHERE userId = ?', [userId]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', userAddresses });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- GET BILLING ADDRESSES -- //
    router.get('/billing-addresses', verifyJWTToken,
        async (req, res) => {
            try {
                const userId = req.decodedToken.userId;

                // -- CHECK BILLING ADDRESSES -- //
                const [userBillingAddresses] = await pool.query('SELECT * FROM user_billing_addresses WHERE userId = ?', [userId]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', userBillingAddresses });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- GET GIFT CARDS -- //
    router.get('/gift-cards', verifyJWTToken,
        async (req, res) => {
            try {
                const userId = req.decodedToken.userId;

                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const user = users[0];

                // -- GET GIDT CARDS -- //
                const [giftCards] = await pool.query('SELECT gc.*, u.firstName, u.lastName FROM gift_cards gc INNER JOIN users u ON u.id = gc.userId WHERE gc.userId = ? AND gc.cityId = ? AND gc.status = ?', [userId, user.cityId, true]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', giftCards });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- ADD ADDRESSES -- //
    router.post('/add-address', [
        body('title').notEmpty().withMessage('Başlık zorunludur'),
        body('cityId').notEmpty().withMessage('Şehir zorunludur').isInt().withMessage('Şehir ID geçerli bir sayı olmalıdır'),
        body('district').optional().isString().withMessage('İlçe string olmalıdır'), // districtId yerine district
        body('fullAddress').notEmpty().withMessage('Açık adres zorunludur'),
        body('identityNumber').notEmpty().withMessage('Kimlik numarası zorunludur')
            .isLength({ min: 11, max: 11 }).withMessage('Kimlik numarası 11 haneli olmalıdır'),
        body('email').notEmpty().withMessage('E-posta zorunludur')
            .isEmail().withMessage('Geçerli bir e-posta adresi olmalıdır'),
        body('phoneNumber').notEmpty().withMessage('Telefon numarası zorunludur')
            .isLength({ max: 13 }).withMessage('Telefon numarası en fazla 13 haneli olmalıdır'),
        body('postalCode').optional({ checkFalsy: true }).isInt().withMessage('Posta kodu geçerli bir sayı olmalıdır')
    ], verifyJWTToken, async (req, res) => {
        console.log("Gelen request body:", req.body);

        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                console.log("Doğrulama hataları:", errors.array()); // Hata detaylarını logla
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(400).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            let { title, cityId, district, postalCode, fullAddress, identityNumber, email, phoneNumber } = req.body;

            postalCode = postalCode || 0; // Boşsa 0 ata
            let districtId = district || null; // district'i districtId olarak kullan

            const [userAddresses] = await pool.query(
                'SELECT * FROM user_addresses WHERE userId = ? AND title = ?',
                [userId, title]
            );

            if (userAddresses.length > 0) {
                return res.status(400).json({ type: 'error', error: 'Aynı isimle adres mevcut!' });
            }

            await pool.query(
                `INSERT INTO user_addresses
                (title, identityNumber, email, phoneNumber, cityId, districtId, postalCode, fullAddress, userId)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [title, identityNumber, email, phoneNumber, cityId, districtId, postalCode, fullAddress, userId]
            );

            const [address] = await pool.query(
                'SELECT * FROM user_addresses WHERE userId = ? ORDER BY createdAt DESC LIMIT 1',
                [userId]
            );

            res.status(200).json({ type: 'success', address });
        } catch (err) {
            console.error("HATA:", err);
            res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
    });

    // -- DELETE ADDRESSES -- //
    router.post('/delete-address',
        [
            body('addressId').notEmpty(),
        ], verifyJWTToken,
        async (req, res) => {
            try {
                // -- VALIDATE -- //
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    const errorMessages = errors.array().map((error) => error.msg);
                    const errorMessageString = errorMessages.join(', ');
                    return res.status(200).json({ type: 'error', error: errorMessageString });
                }

                const { addressId } = req.body;

                await pool.query('DELETE FROM user_addresses WHERE id = ?', [addressId]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', message: 'Adres başarı ile kayıtlı adreslerden kaldırıldı.' });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- ADD BILLING ADDRESSES -- //
    router.post('/add-billing-address',
        [
            body('title').notEmpty().withMessage('Başlık zorunludur'),
            body('city').notEmpty().withMessage('Şehir zorunludur'),
            body('district').notEmpty().withMessage('İlçe zorunludur'),
            body('neighborhood').notEmpty().withMessage('Mahalle zorunludur'),
            body('postalCode').notEmpty().withMessage('Posta kodu zorunludur'),
            body('fullAddress').notEmpty().withMessage('Açık adres zorunludur'),
            body('identityNumber').notEmpty().withMessage('Kimlik numarası zorunludur')
                .isLength({ min: 11, max: 11 }).withMessage('Kimlik numarası 11 haneli olmalıdır'),
            body('email').notEmpty().withMessage('E-posta zorunludur')
                .isEmail().withMessage('Geçerli bir e-posta adresi olmalıdır'),
            body('phoneNumber').notEmpty().withMessage('Telefon numarası zorunludur')
                .isLength({ max: 13 }).withMessage('Telefon numarası en fazla 13 haneli olmalıdır'),
        ], verifyJWTToken,
        async (req, res) => {
            try {
                // -- VALIDATE -- //
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    const errorMessages = errors.array().map((error) => error.msg);
                    const errorMessageString = errorMessages.join(', ');
                    return res.status(200).json({ type: 'error', error: errorMessageString });
                }

                const userId = req.decodedToken.userId;

                const { title, city, district, neighborhood, postalCode, fullAddress, identityNumber, email, phoneNumber } = req.body;

                // -- CHECK BILLING ADDRESSES -- //
                const [userBillingAddresses] = await pool.query('SELECT * FROM user_billing_addresses WHERE userId = ? AND title = ?', [userId, title]);
                if (userBillingAddresses.length > 0) {
                    return res.status(200).json({ type: 'error', error: 'Aynı isimle adres mevcut!' });
                }

                await pool.query('INSERT INTO user_billing_addresses SET title = ?, identityNumber = ?, email = ?, phoneNumber = ?, cityId = ?, districtId = ?, neighborhoodId = ?, postalCode = ?, fullAddress = ?, userId = ?',
                    [title, identityNumber, email, phoneNumber, city, district, neighborhood, postalCode, fullAddress, userId]);

                const [address] = await pool.query('SELECT * FROM user_billing_addresses WHERE userId = ?', [userId]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', address });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- GET HOLLY POINTS -- //
    router.get('/holly-points', verifyJWTToken,
        async (req, res) => {
            try {
                const userId = req.decodedToken.userId;

                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }

                const hollyPoints = users[0].hollyPoints;

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', hollyPoints });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- VALIDATE USER -- //
    router.post('/validate', verifyJWTToken,
        async (req, res) => {
            try {
                const userId = req.decodedToken.userId;

                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }

                // -- CHECK USER BAN -- //
                if (users[0].status === 0) {
                    return res.status(200).json({ type: 'error', error: 'Bu sunucudan yasaklandınız!' });
                }

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', message: 'Giriş başarılı!' });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- LOGIN -- //
    router.post('/login',

        async (req, res) => {
            // -- VALIDATE -- //
            const { phoneNumber, password } = req.body;

            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            try {
                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE phoneNumber = ?', [phoneNumber]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Telefon numarası veya şifre hatalı!' });
                }

                // -- CHECK PASSWORD -- //
                const user = users[0];
                const isPasswordMatch = await bcrypt.compare(password, user.password);
                if (!isPasswordMatch) {
                    return res.status(200).json({ type: 'error', error: 'Telefon numarası veya şifre hatalı!' });
                }

                // -- CHECK USER BAN -- //
                if (user.status === 0) {
                    return res.status(200).json({ type: 'error', error: 'Bu sunucudan yasaklandınız!' });
                }

                // -- HANDLE SUCCESS -- //
                const token = jwt.sign({ userId: user.id }, secretKey, { algorithm: 'HS256', expiresIn: '1w' });
                res.status(200).json({ type: 'success', message: 'Giriş başarılı!', token });
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- REGISTER -- //
    router.post('/register',
        async (req, res) => {
            const { firstName, lastName, phoneNumber, email, password, dateOfBirth } = req.body;

            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                return res.status(200).json({ type: 'error', error: errorMessages.join(', ') });
            }

            const code = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
            const mes = `Hesap oluşturma işlemi için kodunuz: ${code}`;

            try {
                let formattedDateOfBirth = null;

                // 📌 **Doğum tarihi varsa, format kontrolü yap**
                if (dateOfBirth) {
                    formattedDateOfBirth = moment(dateOfBirth, 'DD.MM.YYYY').format('YYYY-MM-DD');
                    if (!moment(formattedDateOfBirth, 'YYYY-MM-DD', true).isValid()) {
                        return res.status(200).json({ type: 'error', error: 'Doğum tarihi geçerli formatta değil' });
                    }
                }

                // -- CHECK USER -- //
                const [existingUsers] = await pool.query('SELECT * FROM users WHERE phoneNumber = ? OR email = ?', [phoneNumber, email]);
                if (existingUsers.length > 0) {
                    return res.status(200).json({ type: 'error', error: 'Aynı bilgiler ile kayıtlı kullanıcı bulundu!' });
                }

                var userId;

                // -- HASH THE PASSWORD -- //
                const hashedPassword = await bcrypt.hash(password, 10);

                // -- GENERATE REFERRAL CODE -- //
                const referralCode = generateRandomReferralCode(8);

                // -- CHECK PENDING USER -- //
                const [existingPendingUsers] = await pool.query('SELECT * FROM pending_users WHERE phoneNumber = ? OR email = ?', [phoneNumber, email]);
                if (existingPendingUsers.length > 0) {
                    userId = existingPendingUsers[0].id;
                } else {
                    // -- CREATE USER -- //
                    const level = [
                        [
                            { "task": "Uygulamayı indir.", "target": 1, "value": 1, "complete": true },
                            { "task": "Uygulamayı puanla.", "target": 1, "value": 0, "complete": false },
                            { "task": "Hediye çarkını çevir.", "target": 1, "value": 0, "complete": false },
                            { "task": "Hesap QR'ını okut ve Holly Puan kazan.", "target": 1, "value": 0, "complete": false },
                            { "task": "1 Holly Snap paylaş.", "target": 1, "value": 0, "complete": false }
                        ]
                    ];

                    const [results] = await pool.query(
                        'INSERT INTO pending_users SET firstName = ?, lastName = ?, phoneNumber = ?, email = ?, password = ?, dateOfBirth = ?, referralCode = ?, level = ?, hollyPoints = ?, status = ?',
                        [firstName, lastName, phoneNumber, email, hashedPassword, formattedDateOfBirth, referralCode, JSON.stringify(level), 0, 0]
                    );

                    userId = results.insertId;
                }

                result = await sendSms(phoneNumber, mes);
                console.log(mes);

                // -- STATUS CONTROL -- //
                const statusCode = result?.response?.status[0]?.code[0];
                const message = result?.response?.status[0]?.message[0];

                if (statusCode === '200') {
                    return res.status(200).json({ type: "success", message: message, code: code, userId: userId });
                } else {
                    return res.status(200).json({ type: "error", error: message });
                }
            } catch (err) {
                logger.error(err);
                return res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        }
    );


    // -- VERIFY REGISTER -- //
    router.post('/verify-register',
        [
            body('userId').notEmpty().withMessage('Id zorunludur'),
        ],
        async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const { userId } = req.body;

            try {
                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM pending_users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kayıtlı kullanıcı bulunamadı!' });
                }

                const user = users[0];

                await pool.query('INSERT INTO users SET cityId = ?, firstName = ?, lastName = ?, phoneNumber = ?, email = ?, password = ?, dateOfBirth = ?, referralCode = ?, level = ?, hollyPoints = ?, status = ?, image = ?',
                    [1, user.firstName, user.lastName, user.phoneNumber, user.email, user.password, user.dateOfBirth, user.referralCode, user.level, user.hollyPoints, 1, 'defaultUser.webp']);

                await pool.query('UPDATE users SET image = ? WHERE id = ?', ["defaultUser.webp", userId]);

                await pool.query('DELETE FROM pending_users WHERE id = ?', [userId]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success', message: 'Kayıt başarılı!' });
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    // -- SEND SMS -- //
    router.post('/send-sms', async (req, res) => {
        const { phoneNumber } = req.body;
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(200).json({ type: 'error', error: errors.array().map(e => e.msg).join(', ') });
        }

        const [users] = await pool.query('SELECT email FROM users WHERE phoneNumber = ?', [phoneNumber]);
        if (users.length === 0) {
          return res.status(200).json({ type: 'error', error: 'Telefon numarası kayıtlı değil!' });
        }

        const { email } = users[0];
        const code = Math.floor(Math.random() * 9000) + 1000;
        const smsMessage = `Şifre sıfırlama için kodunuz: ${code}`;

        try {
          const smsResult = await sendSms(phoneNumber, smsMessage);
          const statusCode = smsResult.response.status[0].code[0];

          if (statusCode === '200') {
            // Mail gönderimini paralel başlat
            await sendMailOtp(email, code);

            return res.status(200).json({
              type: 'success',
              message: 'OTP SMS ve e‑mail olarak gönderildi.',
              code
            });
          } else {
            return res.status(200).json({ type: 'error', error: smsResult.response.status[0].message[0] });
          }
        } catch (err) {
          console.error(err);
          return res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
      });

    // -- RESET PASSWORD -- //
    router.post('/reset-password', [
        body('phoneNumber').notEmpty().withMessage('Telefon numarası zorunludur'),
        body('password').notEmpty().withMessage('Yeni şifre zorunludur')
            .isLength({ min: 6 }).withMessage('Yeni şifre en az 8 karakter olmak zorundadır')
            .isLength({ max: 64 }).withMessage('Yeni şifre en fazla 64 karakter olmak zorundadır')
    ], async (req, res) => {
        // -- VALIDATE -- //
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map((error) => error.msg);
            const errorMessageString = errorMessages.join(', ');
            return res.status(200).json({
                type: 'error',
                error: errorMessageString
            });
        }

        const { phoneNumber, password } = req.body;

        try {
            // -- CHECK USER -- //
            const [users] = await pool.query('SELECT * FROM users WHERE phoneNumber = ?', [phoneNumber]);
            if (users.length === 0) {
                return res.status(200).json({
                    type: 'error',
                    error: 'Kullanıcı bulunamadı!'
                });
            }

            // -- HASH THE PASSWORD -- //
            const hashedPassword = await bcrypt.hash(password, 10);

            // -- UPDATE USER -- //
            const [result] = await pool.query(
                'UPDATE users SET password = ? WHERE phoneNumber = ?',
                [hashedPassword, phoneNumber]
            );

            if (result.affectedRows === 0) {
                return res.status(200).json({
                    type: 'error',
                    error: 'Şifre güncellenemedi!'
                });
            }

            // -- HANDLE SUCCESS -- //
            res.status(200).json({
                type: 'success',
                message: 'Şifre başarıyla güncellendi!'
            });

        } catch (err) {
            // -- HANDLE ERROR -- //
            console.error('Şifre sıfırlama hatası:', err);
            res.status(500).json({
                type: 'error',
                error: 'Sunucu hatası oluştu'
            });
        }
    });

    return router;
};
