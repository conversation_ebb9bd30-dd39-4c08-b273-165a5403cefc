const mysql = require('mysql2/promise');
require('dotenv').config();

async function createScannedQRCodesTable() {
    try {
        // Create connection
        const connection = await mysql.createConnection({
            host: process.env.MYSQL_HOST,
            port: process.env.MYSQL_PORT,
            user: process.env.MYSQL_USER,
            password: process.env.MYSQL_PASSWORD,
            database: process.env.MYSQL_DATABASE
        });

        console.log('Connected to database');

        // SQL to create the scanned_qr_codes table
        const createTableSQL = `
        CREATE TABLE IF NOT EXISTS scanned_qr_codes (
            id int(11) NOT NULL AUTO_INCREMENT,
            userId int(11) NOT NULL,
            qrData varchar(255) NOT NULL,
            pointsEarned int(11) NOT NULL DEFAULT 0,
            scannedAt timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY userId_qrData (userId, qrData),
            <PERSON><PERSON><PERSON> userId (userId),
            KEY qrData (qrData)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `;

        // Execute the SQL
        await connection.execute(createTableSQL);
        console.log('scanned_qr_codes table created or already exists');

        // Close the connection
        await connection.end();
        console.log('Connection closed');
    } catch (error) {
        console.error('Error creating scanned_qr_codes table:', error);
    }
}

// Run the function
createScannedQRCodesTable();
