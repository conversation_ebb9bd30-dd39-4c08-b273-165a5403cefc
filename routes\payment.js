const express = require('express');
const router = express.Router();
const microtime = require('microtime');
const { body, validationResult } = require('express-validator');
const verifyJWTToken = require('../middleware/verifyJWTToken');
const zlib = require('zlib');
const payment = require('../components/payment');
const sendMail = require('../components/sendMail');
const sendSms = require('../components/sendSms');

module.exports = (pool) => {
    router.post("/store-card",
        [
            body('cc_owner').notEmpty().withMessage('Kart sahibi bilgisi zorunludur'),
            body('card_number').notEmpty().withMessage('Kart numarası bilgisi zorunludur'),
            body('expiry_month').notEmpty().withMessage('Son kullanım ay bilgisi zorunludur'),
            body('expiry_year').notEmpty().withMessage('<PERSON> kullanım yıl bilgisi zorunludur'),
            body('cvv').notEmpty().withMessage('CVV bilgisi zorunludur'),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;

            const { cc_owner, card_number, expiry_month, expiry_year, cvv } = req.body;

            const user_ip = req.header('x-forwarded-for');
            const merchantOid = "STCM" + microtime.now();

            try {
                // -- GET UTOKEN -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const user = users[0];

                // -- CHECK ADDRESSES -- //
                const [userAddresses] = await pool.query('SELECT * FROM user_addresses WHERE userId = ? ORDER BY createdAt DESC LIMIT 1', [userId]);
                if (userAddresses.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Adres bulunamadı!' });
                }
                const userAddress = userAddresses[0]

                const paymentResult = await payment.storeCard(`${user.firstName} ${user.lastName}`, userAddress.fullAddress, user.phoneNumber, user.email, user_ip, user.utoken, merchantOid, cc_owner, card_number, expiry_month, expiry_year, cvv);
                if (paymentResult.type == "success") {
                    // -- HANDLE SUCCESS -- //
                    await pool.query('INSERT INTO store_card_requests SET userId = ?, merchantOid = ?', [userId, merchantOid]);
                    return res.status(200).json({ type: 'success', paymentLink: paymentResult.paymentLink });
                } else {
                    // -- HANDLE PAYMENT ERROR -- //
                    return res.status(200).json({ type: 'error', message: 'Ödeme hatası' });
                }
            } catch (err) {
                console.error(err);
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }

        });

    router.post("/delete-card",
        [
            body('ctoken').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;

            const { ctoken } = req.body;

            try {
                // -- GET UTOKEN -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const user = users[0];

                const paymentResult = await payment.deleteCard(user.utoken, ctoken);
                if (paymentResult.status == "success") {
                    return res.status(200).json({ type: 'success', message: "Kart başarı ile silindi!" });
                } else {
                    return res.status(400).json({ type: 'error', error: paymentResult.err_msg });
                }
            } catch (err) {
                console.error(err);
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }

        });

    router.post("/refund-ticket", verifyJWTToken, async (req, res) => {
        try {
            // Extract user ID from the token
            const userId = req.decodedToken.userId;

            const { ticketId, tickets } = req.body;

            // Check if the user is an admin
            const [admins] = await pool.query('SELECT * FROM admins WHERE authorityLevel = 1 AND id = ?', [userId]);
            if (admins.length === 0) {
                return res.status(403).json({ type: 'error', error: 'Unauthorized' });
            }

            if (ticketId == -1 && tickets.length > 0) {
                let totalRefund = 0;

                for (const itemId of tickets) {
                    const [ticketData] = await pool.query('SELECT ut.*, u.firstName, u.phoneNumber, c.name as concertName FROM user_tickets ut JOIN users u ON u.id = ut.userId JOIN concert_tickets ct ON ct.id = ut.ticketId JOIN concerts c ON c.id = ct.concertId WHERE ut.id = ?', [itemId]);

                    if (ticketData.length === 0) {
                        continue;
                    }

                    const ticket = ticketData[0];

                    if (ticket.status === 4) {
                        continue;
                    }

                    const paymentResult = await payment.refund(ticket.paidPrice, ticket.merchantOid);

                    if (paymentResult.status === "success") {
                        await pool.query('UPDATE user_tickets SET status = 4 WHERE id = ?', [itemId]);
                        await pool.query('INSERT INTO refunded_tickets SET ticketId = ?', [itemId]);

                        await sendMail(pool, itemId, true);
                        const message = `Merhaba ${ticket.firstName}, Holly Stone dan almış olduğunuz ${ticket.concertName} konserine ait biletiniz iade edilmiştir.`;
                        await sendSms(ticket.phoneNumber, message);

                        totalRefund += 1;
                    }
                }

                return res.status(200).json({ type: 'success', message: `${totalRefund} adet bilet iade edildi!` });
            } else if (ticketId !== -1) {
                // Get ticket details
                const [ticketData] = await pool.query('SELECT ut.*, u.firstName, u.phoneNumber, c.name as concertName FROM user_tickets ut JOIN users u ON u.id = ut.userId JOIN concert_tickets ct ON ct.id = ut.ticketId JOIN concerts c ON c.id = ct.concertId WHERE ut.id = ?', [ticketId]);

                if (ticketData.length === 0) {
                    return res.status(404).json({ type: 'error', error: 'Bilet bulunamadı!' });
                }

                const ticket = ticketData[0];

                if (ticket.status === 4) {
                    return res.status(400).json({ type: 'error', error: 'Bilet zaten iptal edilmiş!' });
                }

                // Perform the refund
                const paymentResult = await payment.refund(ticket.paidPrice, ticket.merchantOid);

                if (paymentResult.status === "success") {
                    await pool.query('UPDATE user_tickets SET status = 4 WHERE id = ?', [ticketId]);
                    await pool.query('INSERT INTO refunded_tickets SET ticketId = ?', [ticketId]);

                    await sendMail(pool, ticketId, true);
                    // Burada hata var !!
                    // const message = `Merhaba ${ticket.firstName}, Holly Stone dan almış olduğunuz ${ticket.concertName} konserine ait biletiniz iade edilmiştir.`;
                    const message = `Merhaba, Holly Stone dan almış olduğunuz biletiniz iade edilmiştir.`;
                    const smsResult = await sendSms(ticket.phoneNumber, message);
                    console.log(smsResult);

                    return res.status(200).json({ type: 'success', message: "Bilet iade edildi" });
                } else {
                    return res.status(400).json({ type: 'error', error: paymentResult.err_msg });
                }
            } else {
                return res.status(400).json({ type: 'error', error: 'Eksik bilgi gönderildi!' });
            }
        } catch (err) {
            console.error(err);
            res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
    });

    router.post("/refund-order", verifyJWTToken, async (req, res) => {
        try {
            // Extract user ID from the token
            const userId = req.decodedToken.userId;

            const { orderId } = req.body;

            // Check if the user is an admin
            const [admins] = await pool.query('SELECT * FROM admins WHERE authorityLevel = 1 AND id = ?', [userId]);
            if (admins.length === 0) {
                return res.status(403).json({ type: 'error', error: 'Unauthorized' });
            }

            const [orderData] = await pool.query('SELECT * FROM orders WHERE id = ?', [orderId]);

            if (orderData.length === 0) {
                return res.status(404).json({ type: 'error', error: 'Sipariş bulunamadı!' });
            }

            const order = orderData[0];

            if (order.status === 4) {
                return res.status(400).json({ type: 'error', error: 'Sipariş zaten iade edilmiş!' });
            }

            const paymentResult = await payment.refund(order.price, order.merchantOid);

            if (paymentResult.status === "success") {
                await pool.query('UPDATE orders SET status = 4 WHERE id = ?', [orderId]);
                await pool.query('INSERT INTO refunded_orders SET orderId = ?', [orderId]);

                return res.status(200).json({ type: 'success', message: "Sipariş iade edildi" });
            } else {
                return res.status(400).json({ type: 'error', error: paymentResult.err_msg });
            }
        } catch (err) {
            console.error(err);
            res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
    });

    router.post("/transfer-payment", verifyJWTToken, async (req, res) => {
        try {
            // Extract user ID from the token
            const userId = req.decodedToken.userId;

            const { concertId } = req.body;

            if (concertId == null || concertId === undefined) {
                console.log(concertId);
                return res.status(400).json({ type: 'error', error: 'Eksik bilgi gönderildi!' });
            }

            // Check if the user is an admin
            const [admins] = await pool.query('SELECT * FROM admins WHERE authorityLevel = 1 AND id = ?', [userId]);
            if (admins.length === 0) {
                return res.status(403).json({ type: 'error', error: 'Unauthorized' });
            }

            // Get settings
            const [settings] = await pool.query('SELECT * FROM settings LIMIT 1');
            const commission = settings[0].commission;

            const [transferData] = await pool.query(`
            SELECT ut.id as userTicketId, ut.paidPrice as price, ut.merchantOid, v.bankAccountName as transferName, v.iban as transferIban
            FROM user_tickets ut
            JOIN concert_tickets ct ON ut.ticketId = ct.id
            JOIN concerts c ON ct.concertId = c.id
            JOIN vendors v ON c.vendorId = v.id
            LEFT JOIN payment_transfer pt ON ut.id = pt.ticketId
            WHERE ut.status IN (1, 2)
            AND ut.payment = true
            AND ut.paidPrice > 0
            AND pt.ticketId IS NULL
            AND c.id = ?;
            `, [concertId]);

            if (transferData.length === 0) {
                return res.status(400).json({ type: 'error', error: 'Tüm ödemeler aktarılmış!' });
            }

            for (const ticket of transferData) {
                const returnAmount = ticket.price * 100;
                const transferFee = ticket.price * commission;
                const submerchantAmount = returnAmount - transferFee;
                const transId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

                var paymentResult = { status: "" };

                if (ticket.merchantOid) {
                    paymentResult = await payment.transferPayment(returnAmount, submerchantAmount, ticket.merchantOid, transId, ticket.transferName, ticket.transferIban);
                } else {
                    paymentResult.status = "success";
                }

                if (paymentResult.status === "success") {
                    await pool.query('INSERT INTO payment_transfer SET ticketId = ?, transId = ?', [ticket.userTicketId, transId]);
                } else {
                    return res.status(400).json({ type: 'error', error: paymentResult.err_msg });
                }
            }

            return res.status(200).json({ type: 'success', message: 'Aktarım tamamlandı' });

        } catch (err) {
            console.error(err);
            res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
    });

    router.get("/card-list", verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            // -- GET UTOKEN -- //
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }

            const utoken = users[0].utoken;
            if (!utoken) {
                return res.status(200).json({ type: 'success', list: [] });

            }

            const result = await payment.getCardList(utoken);
            if (result.status == "error") {
                return res.status(200).json({ type: 'success', list: [] });
            }

            return res.status(200).json({ type: 'success', list: result });
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get("/view-html", async (req, res) => {
        const { htmlContent } = req.query;

        const decodedHtmlContent = Buffer.from(decodeURIComponent(htmlContent), 'base64');
        const uncompressedHtmlContent = zlib.inflateSync(decodedHtmlContent).toString();

        const renderedHtml = `
            <!doctype html>
            <html lang="tr">
            <head>
                <meta charset="UTF-8">
                <title>Paytr Response</title>
            </head>
            <body>
                <div id="paytr-response">
                    ${uncompressedHtmlContent}
                </div>
            </body>
            </html>
        `;

        return res.send(renderedHtml);
    });

    router.post("/callback-fail", async (req, res) => {
        return res.status(200).json(req.body);
    });

    router.get("/callback-success", async (req, res) => {
        return res.status(200).json(req.body);
    });

    router.post("/callback", async (req, res) => {
        jsonSting = JSON.stringify(req.body).replace(/\\[rn]|\\/g, '');
        console.log(jsonSting);

        const callback = req.body;
        const token = await payment.tokenGenerator(callback.merchant_oid, callback.status, callback.total_amount);

        if (token !== callback.hash) {
            return res.status(400).send("PAYTR notification failed: bad hash");
        }

        try {
            // -- STORE CALLBACK INFO -- //
            await pool.query('INSERT INTO paytr_callback SET hash = ?, merchantOid = ?, paymentAmount = ?, totalAmount = ?, installmentCount = ?, test = ?, status = ?, failMessage = ?',
                [callback.hash, callback.merchant_oid, callback.payment_amount, callback.total_amount, callback.installment_count, callback.test_mode, callback.status === 'success' ? true : false, callback.failed_reason_msg]);
        } catch (error) {
            console.error(error);
        }

        if (callback.status === 'success') {
            const [storecardRequests] = await pool.query('SELECT * FROM store_card_requests WHERE merchantOid = ? ORDER BY createdAt DESC LIMIT 1', [callback.merchant_oid]);
            if (storecardRequests.length > 0) {
                const storecardRequest = storecardRequests[0];
                await pool.query('UPDATE users SET uToken = ? WHERE id = ?', [callback.utoken, storecardRequest.userId]);
                return;
            }

            const [orders] = await pool.query(`SELECT tc.title as city, td.title as district, tn.title as neighborhood, ua.title as addressTitle, ua.fullAddress, ua.identityNumber, u.firstName, u.lastName, u.phoneNumber, u.email, o.id, o.vendorId, o.createdAt, o.price, o.taxExcludingPrice, o.efatura, o.taxNumber, o.taxOffice FROM orders o INNER JOIN order_items oi ON o.id = oi.orderId INNER JOIN shop_products sp ON oi.productId = sp.id INNER JOIN users u ON o.userId = u.id INNER JOIN user_addresses ua ON o.addressId = ua.id INNER JOIN turkey_cities tc ON ua.cityId = tc.key INNER JOIN turkey_districts td ON ua.districtId = td.key INNER JOIN turkey_neighborhoods tn ON ua.neighborhoodId = tn.key WHERE o.merchantOid = ? GROUP BY o.merchantOid, tc.title, td.title, tn.title, ua.title, ua.fullAddress, ua.identityNumber, u.firstName, u.lastName, u.phoneNumber, u.email, o.id, o.vendorId, o.createdAt, o.price, o.taxExcludingPrice, o.efatura, o.taxNumber, o.taxOffice`, [callback.merchant_oid]);
            if (orders.length > 0) {
                const order = orders[0];

                const [orderItems] = await pool.query(`SELECT sp.id, oi.quantity, oi.price, sp.name, sp.images, sp.taxRate, sp.createdAt FROM order_items oi INNER JOIN shop_products sp ON oi.productId = sp.id WHERE oi.orderId = ? GROUP BY sp.id`, [order.id]);

                const formattedOrderItems = orderItems.map(item => ({
                    ...item,
                    image: item.images.split(",")[0]
                }));

                await pool.query('UPDATE orders SET status = ?, payment = ? WHERE id = ?', [1, true, order.id]);

                const code = `HL${order.id}T${new Date(order.createdAt).getFullYear().toString().slice(-2)}${String(new Date(order.createdAt).getMonth() + 1).padStart(2, '0')}${String(new Date(order.createdAt).getDate()).padStart(2, '0')}`;
                await pool.query('INSERT INTO invoices SET orderId = ?, orderCode = ?, orderDate = ?, vendorId = ?, billingName = ?, billingAddress = ?, billingTown = ?, billingCity = ?, billingMobilePhone = ?, identityNumber = ?, email = ?, taxExcludingPrice = ?, taxIncludingPrice = ?, orderDetails = ?, efatura = ?, taxNumber = ?, taxOffice = ?, orderStatusId = ?',
                    [order.id, code, order.createdAt, order.vendorId, `${order.firstName} ${order.lastName}`, order.fullAddress, order.district, order.city, order.phoneNumber, order.identityNumber, order.email, order.taxExcludingPrice, order.price, JSON.stringify(formattedOrderItems), order.efatura, order.taxNumber, order.taxOffice, 0]);
            }

            const [tickets] = await pool.query(`SELECT ut.*, ct.taxRate, c.name, c.image, c.vendorId FROM user_tickets ut INNER JOIN concert_tickets ct ON ut.ticketId = ct.id INNER JOIN concerts c ON ct.concertId = c.id WHERE ut.merchantOid = ?`, [callback.merchant_oid]);
            if (tickets.length > 0) {
                tickets.forEach(async (ticket) => {
                    const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [ticket.userId]);
                    const user = users[0];
                    const [userAddresses] = await pool.query('SELECT *, tc.title as city, td.title as district FROM user_addresses ua INNER JOIN turkey_cities tc ON ua.cityId = tc.key INNER JOIN turkey_districts td ON ua.districtId = td.key WHERE userId = ?', [ticket.userId]);
                    const userAddress = userAddresses[0]
                    await pool.query('UPDATE user_tickets SET status = ?, payment = ? WHERE id = ?', [1, true, ticket.id]);

                    const code = `HL${ticket.id}T${new Date(ticket.createdAt).getFullYear().toString().slice(-2)}${String(new Date(ticket.createdAt).getMonth() + 1).padStart(2, '0')}${String(new Date(ticket.createdAt).getDate()).padStart(2, '0')}`;
                    await pool.query('INSERT INTO invoices SET orderId = ?, orderCode = ?, orderDate = ?, vendorId = ?, billingName = ?, billingAddress = ?, billingTown = ?, billingCity = ?, billingMobilePhone = ?, identityNumber = ?, email = ?, taxExcludingPrice = ?, taxIncludingPrice = ?, orderDetails = ?, efatura = ?, taxNumber = ?, taxOffice = ?, orderStatusId = ?',
                        [ticket.id, code, ticket.createdAt, ticket.vendorId, `${user.firstName} ${user.lastName}`, userAddress.fullAddress, userAddress.district, userAddress.city, user.phoneNumber, userAddress.identityNumber, user.email, ticket.taxExcludingPrice, ticket.paidPrice, JSON.stringify([{ id: ticket.id, quantity: 1, price: ticket.paidPrice, name: ticket.name, image: ticket.image, taxRate: ticket.taxRate, createdAt: ticket.createdAt }]), false, null, null, 0]);
                });
            }
        }

        res.send('OK');

    });

    router.get("/info", async (req, res) => {
        const { merchantOid } = req.query;

        if (!merchantOid) {
            return res.status(400).json({ type: "error", error: "merchantOid bulunamadı!" });
        }

        try {
            const paymentInfo = await payment.getPaymentInfo(merchantOid);
            return res.status(200).json(paymentInfo);
        } catch (error) {
            return res.status(500).json({ error: 'Ödeme bilgisi alınamadı' });
        }
    });

    router.post("/wallet-pay", verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;
            const { amount, concertId, quantity = 1 } = req.body;

            // Gerekli parametreleri kontrol et
            if (!amount || !concertId) {
                return res.status(400).json({ type: 'error', error: 'Gerekli parametreler eksik: amount ve concertId zorunludur.' });
            }

            // Quantity kontrolü
            const requestedQuantity = parseInt(quantity);
            if (isNaN(requestedQuantity) || requestedQuantity <= 0) {
                return res.status(200).json({ type: 'error', error: 'Geçersiz adet miktarı.' });
            }

            // Kullanıcının cüzdanını kontrol et
            const [walletData] = await pool.query('SELECT id, balance FROM wallets WHERE userId = ?', [userId]);

            if (walletData.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcının cüzdanı bulunmamaktadır.' });
            }

            const walletId = walletData[0].id;
            const currentBalance = parseFloat(walletData[0].balance);
            const requestedAmount = parseFloat(amount);

            if (isNaN(currentBalance) || isNaN(requestedAmount)) {
                return res.status(200).json({ type: 'error', error: 'Geçersiz bakiye veya miktar formatı.' });
            }

            // Bakiye kontrolü
            if (currentBalance < requestedAmount) {
                return res.status(200).json({ type: 'error', error: 'Yetersiz bakiye. Lütfen cüzdanınıza para yükleyin.' });
            }

            // Konser ve bilet bilgilerini al
            const [concertData] = await pool.query(
                `SELECT c.*, ct.id as ticketId, ct.price, ct.taxRate, ct.type, ct.is_refundable,
                v.id as vendorId, v.name as vendorName, v.latitude, v.longitude, s.name as stageName
                FROM concerts c
                JOIN concert_tickets ct ON c.id = ct.concertId
                JOIN vendors v ON c.vendorId = v.id
                LEFT JOIN stages s ON c.stageId = s.id
                WHERE c.id = ? AND ct.isDeleted = false LIMIT 1`,
                [concertId]
            );

            if (concertData.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Konser bulunamadı.' });
            }

            const concert = concertData[0];
            const ticketId = concert.ticketId;
            const ticketPrice = parseFloat(concert.price);

            // Kalan kota kontrolü
            const [quotaCheck] = await pool.query(
                `SELECT
                    CASE WHEN ct.quota IS NOT NULL AND ct.quota != 0
                    THEN ct.quota - IFNULL((SELECT COUNT(*) FROM user_tickets WHERE ticketId = ct.id), 0)
                    ELSE NULL END AS remaining_quota
                FROM concert_tickets ct
                WHERE ct.id = ?`,
                [ticketId]
            );

            if (quotaCheck.length > 0 && quotaCheck[0].remaining_quota !== null && quotaCheck[0].remaining_quota <= 0) {
                return res.status(200).json({ type: 'error', error: 'Bu bilet için kota dolmuştur.' });
            }

            // Kota yeterli mi kontrol et
            if (quotaCheck.length > 0 && quotaCheck[0].remaining_quota !== null && quotaCheck[0].remaining_quota < calculatedQuantity) {
                return res.status(200).json({ type: 'error', error: `Sadece ${quotaCheck[0].remaining_quota} adet bilet kalmıştır.` });
            }

            // Frontend toplam fiyatı gönderiyor, quantity'yi hesaplayalım
            // Eğer amount bilet fiyatının katı değilse hata ver
            if (requestedAmount % ticketPrice !== 0) {
                return res.status(200).json({
                    type: 'error',
                    error: `Geçersiz ödeme miktarı. Bilet fiyatı: ${ticketPrice} TL`
                });
            }

            // Quantity'yi amount'tan hesapla
            const calculatedQuantity = requestedAmount / ticketPrice;

            // Gönderilen quantity ile hesaplanan quantity eşleşmeli
            if (quantity && calculatedQuantity !== requestedQuantity) {
                return res.status(200).json({
                    type: 'error',
                    error: `Adet ve fiyat eşleşmiyor. ${requestedQuantity} adet × ${ticketPrice} TL = ${requestedQuantity * ticketPrice} TL olmalı`
                });
            }

            // Hesaplanan quantity'yi kullan
            const finalQuantity = quantity ? requestedQuantity : calculatedQuantity;
            const totalPrice = requestedAmount; // Frontend zaten toplam gönderiyor

            // Türkiye saatiyle tarih oluştur
            const turkiyeSaatiyleTarih = new Date();
            turkiyeSaatiyleTarih.setHours(turkiyeSaatiyleTarih.getHours() + 3); // UTC+3

            // Benzersiz sipariş numarası oluştur
            const merchantOid = "WALLET" + Date.now() + userId;

            // Kullanıcı bilgilerini al
            const [userData] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            if (userData.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı.' });
            }
            const user = userData[0];

            // Kullanıcı adres bilgilerini al
            const [addressData] = await pool.query('SELECT * FROM user_addresses WHERE userId = ? ORDER BY createdAt DESC LIMIT 1', [userId]);
            if (addressData.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı adresi bulunamadı.' });
            }
            const address = addressData[0];

            // Transaction için ayrı bağlantı al
            const connection = await pool.getConnection();

            try {
                await connection.beginTransaction();

                // Kullanıcının bakiyesini güncelle
                const updatedBalance = currentBalance - requestedAmount;
                await connection.query('UPDATE wallets SET balance = ? WHERE id = ?', [updatedBalance, walletId]);

                // Wallet transaction kaydı oluştur
                await connection.query(
                    'INSERT INTO transactions (walletId, type, amount, description, createdAt, userId, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
                    [walletId, 'expense', requestedAmount, `Konser bileti satın alımı: ${concert.name}`, turkiyeSaatiyleTarih, userId, 'completed']
                );

                // Vergi hariç fiyatı hesapla
                let taxExcludingPrice = ticketPrice / (1 + (concert.taxRate / 100));

                // Bilet tipini kontrol et (tekli veya çiftli bilet)
                let innerQuantity = 1;
                let price = ticketPrice;
                let bondedTo = null;
                let userTicketIds = [];

                // Bilet tipini kontrol et
                if (concert.type === 1) { // Çift bilet kontrolü
                    innerQuantity = 2;
                    price = ticketPrice / 2;
                    taxExcludingPrice = taxExcludingPrice / 2;
                }

                // Biletleri oluştur
                for (let i = 0; i < calculatedQuantity; i++) {
                    for (let j = 0; j < innerQuantity; j++) {
                    if (bondedTo !== null && j > 0) {
                        // İkinci bilet için bondedTo değerini kullan
                    } else {
                        bondedTo = null; // İlk bilet için bondedTo null
                    }

                    // Önce geçici bir QR kodu oluştur
                    const tempQrCode = "TEMP" + Date.now() + Math.random().toString(36).substring(2, 15);

                    // QR kodu ile birlikte bilet kaydını oluştur
                    const [insertResult] = await connection.query(
                        `INSERT INTO user_tickets
                        (userId, ticketId, bondedTo, status, payment, paidPrice, taxExcludingPrice,
                        merchantOid, qrCode, giftStatus, vendorId, createdAt, updatedAt)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            userId,
                            ticketId,
                            bondedTo,
                            aktif, // status: 1 = aktif
                            true, // payment: true = ödenmiş
                            price,
                            taxExcludingPrice,
                            merchantOid,
                            tempQrCode,
                            'available', // giftStatus: available = hediye edilebilir
                            concert.vendorId, // vendorId: konser sağlayıcısı
                            turkiyeSaatiyleTarih,
                            turkiyeSaatiyleTarih
                        ]
                    );

                    const insertId = insertResult.insertId;
                    userTicketIds.push(insertId);

                    if (concert.type === 1 && j === 0) {
                        bondedTo = insertId; // Çift biletse bir sonrakiyle bağla
                    }

                    // Gerçek QR Kodu oluştur
                    const dayjs = require('dayjs');
                    const microtime = require('microtime');
                    const qrCode = insertId + "HLYSTN" + microtime.now() + "T" + dayjs(concert.date).format("DDMMYYYY");

                    // QR kodu güncelle
                    await connection.query('UPDATE user_tickets SET qrCode = ? WHERE id = ?', [qrCode, insertId]);
                    }
                }

                const userTicketId = userTicketIds[0]; // İlk bilet ID'sini kullan

                // Fatura kodu oluştur
                const code = `HL${userTicketId}T${turkiyeSaatiyleTarih.getFullYear().toString().slice(-2)}${String(turkiyeSaatiyleTarih.getMonth() + 1).padStart(2, '0')}${String(turkiyeSaatiyleTarih.getDate()).padStart(2, '0')}`;

                // Adres bilgilerini al (varsa)
                let town = '';
                let city = '';

                try {
                    // Adres bilgilerini daha detaylı al
                    const [addressDetails] = await pool.query(
                        'SELECT ua.*, tc.title as city, td.title as district FROM user_addresses ua ' +
                        'LEFT JOIN turkey_cities tc ON ua.cityId = tc.key ' +
                        'LEFT JOIN turkey_districts td ON ua.districtId = td.key ' +
                        'WHERE ua.id = ?',
                        [address.id]
                    );

                    if (addressDetails.length > 0) {
                        town = addressDetails[0].district || '';
                        city = addressDetails[0].city || '';
                    }
                } catch (addrErr) {
                    console.error('Adres detayları alınırken hata:', addrErr);
                }

                // Bilet detaylarını hazırla
                const orderDetails = userTicketIds.map(id => ({
                    id: id,
                    quantity: 1,
                    price: concert.type === 1 ? price : ticketPrice, // Çift bilet ise yarı fiyat
                    name: concert.name,
                    image: concert.image || null,
                    taxRate: concert.taxRate,
                    createdAt: turkiyeSaatiyleTarih,
                    stageName: concert.stageName || '',
                    date: concert.date,
                    vendorName: concert.vendorName || '',
                    latitude: concert.latitude || null,
                    longitude: concert.longitude || null
                }));

                // Transaction'u tamamla
                await connection.commit();

                // Bağlantıyı serbest bırak
                connection.release();

                // E-posta göndermeyi dene
                try {
                    const sendMail = require('../components/sendMail');
                    await sendMail(pool, userTicketId, false);
                } catch (mailErr) {
                    console.error('E-posta gönderme hatası:', mailErr);
                    // E-posta hatası işlemi durdurmaz
                }

                // Başarılı yanıt dön
                return res.status(200).json({
                    type: 'success',
                    message: 'Bilet satın alma işlemi başarıyla tamamlandı.',
                    tickets: userTicketIds.map(id => ({
                        ticketId: id,
                        concertName: concert.name,
                        price: concert.type === 1 ? price : ticketPrice,
                        date: concert.date,
                    })),
                    quantity: requestedQuantity,
                    totalPrice: totalPrice, // Toplam fiyat (quantity × ticketPrice)
                    unitPrice: ticketPrice, // Birim fiyat
                    walletBalance: updatedBalance,
                    invoiceCode: code
                });

            } catch (error) {
                // Hata durumunda transaction'u geri al
                await connection.rollback();
                connection.release();
                console.error('Bilet satın alma hatası:', error);
                return res.status(500).json({ type: 'error', error: 'Bilet satın alma işlemi sırasında bir hata oluştu.' });
            }

        } catch (error) {
            console.error('Hata:', error);
            return res.status(500).json({ type: 'error', error: 'Bir hata oluştu, işlem tamamlanamadı.' });
        }
    });

    // Kart Bilgilerini Getirme Endpoint'i
    router.get("/cards/:cardCode", async (req, res) => {
        try {
            console.log('GET /cards/:cardCode isteği alındı:', req.params);

            const { cardCode } = req.params;
            console.log('Kart kodu:', cardCode);

            // Gift Card kontrolü
            const [cardData] = await pool.query('SELECT * FROM gift_cards WHERE code = ?', [cardCode]);
            console.log('Kart verisi:', cardData.length > 0 ? 'Bulundu' : 'Bulunamadı');

            if (cardData.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Kart bulunamadı.'
                });
            }
            const card = cardData[0];

            // Kart durumu kontrolü
            const statusText = card.status === 1 ? 'active' : 'inactive';
            console.log('Kart durumu:', statusText);

            // Son kullanım tarihini al
            const [lastUsageData] = await pool.query(
                'SELECT createdAt FROM gift_card_usage_history WHERE cardId = ? ORDER BY createdAt DESC LIMIT 1',
                [card.id]
            );

            const lastUsed = lastUsageData.length > 0 ? lastUsageData[0].createdAt : null;
            console.log('Son kullanım tarihi:', lastUsed);

            // Kart tipini belirle
            let cardType = "standard";
            if (card.type === 1) cardType = "premium";
            else if (card.type === 2) cardType = "vip";
            console.log('Kart tipi:', cardType);

            // Cache kontrolü için headers
            res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
            res.setHeader('Surrogate-Control', 'no-store');

            // Başarılı yanıt dön
            const responseData = {
                id: card.code, // Kart kodu ID olarak kullanılıyor
                balance: parseFloat(card.value),
                cardType: cardType,
                status: statusText,
                lastUsed: lastUsed,
                code: card.code
            };

            console.log('Yanıt verisi:', responseData);
            return res.status(200).json(responseData);

        } catch (error) {
            console.error('Kart bilgisi getirme hatası:', error);
            return res.status(500).json({
                success: false,
                message: 'Kart bilgileri alınırken bir hata oluştu.',
                error: error.message
            });
        }
    });

    // Karta Bakiye Yükleme Endpoint'i
    router.post("/cards/:cardCode/load", async (req, res) => {
        try {
            console.log('POST /cards/:cardCode/load isteği alındı:', req.params, req.body);

            const { cardCode } = req.params;
            const { amount } = req.body;
            console.log('Kart kodu:', cardCode, 'Miktar:', amount);

            // Miktar kontrolü
            if (!amount) {
                console.log('Hata: Miktar belirtilmedi');
                return res.status(400).json({
                    success: false,
                    message: 'Yüklenecek miktar belirtilmelidir.'
                });
            }

            const requestedAmount = parseFloat(amount);
            if (isNaN(requestedAmount) || requestedAmount <= 0) {
                console.log('Hata: Geçersiz miktar:', amount);
                return res.status(400).json({
                    success: false,
                    message: 'Geçersiz miktar. Pozitif bir sayı girilmelidir.'
                });
            }

            // Gift Card kontrolü
            const [cardData] = await pool.query('SELECT * FROM gift_cards WHERE code = ?', [cardCode]);
            console.log('Kart verisi:', cardData.length > 0 ? 'Bulundu' : 'Bulunamadı');

            if (cardData.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Kart bulunamadı.'
                });
            }
            const card = cardData[0];

            // Kart sahibi bilgilerini al
            const cardOwnerId = card.userId;
            console.log('Kart sahibi ID:', cardOwnerId);

            const [cardOwnerData] = await pool.query('SELECT * FROM users WHERE id = ?', [cardOwnerId]);
            if (cardOwnerData.length === 0) {
                console.log('Hata: Kart sahibi bulunamadı');
                return res.status(404).json({
                    success: false,
                    message: 'Kart sahibi kullanıcı bulunamadı.'
                });
            }

            // Kart durumu kontrolü
            if (card.status !== 1) {
                console.log('Hata: Kart aktif değil, durum:', card.status);
                return res.status(400).json({
                    success: false,
                    message: 'Bu kart aktif değil.'
                });
            }

            // Cache kontrolü için headers
            res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
            res.setHeader('Surrogate-Control', 'no-store');

            // Transaction için ayrı bağlantı al
            console.log('Transaction başlatılıyor...');
            const connection = await pool.getConnection();

            try {
                await connection.beginTransaction();

                // Kart bakiyesini güncelle
                const previousBalance = parseFloat(card.value);
                const newBalance = previousBalance + requestedAmount;
                console.log('Bakiye güncelleniyor:', previousBalance, '+', requestedAmount, '=', newBalance);

                await connection.query('UPDATE gift_cards SET value = ? WHERE id = ?', [newBalance, card.id]);

                // Kullanım geçmişi kaydı oluştur - Kart sahibinin ID'si ile
                console.log('Kullanım geçmişi kaydı oluşturuluyor...');
                const [insertResult] = await connection.query(
                    'INSERT INTO gift_card_usage_history (userId, cardId, amount, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
                    [cardOwnerId, card.id, requestedAmount.toString()]
                );

                const transactionId = "tx" + insertResult.insertId;
                console.log('İşlem ID:', transactionId);

                // Transaction'ı tamamla
                console.log('Transaction tamamlanıyor...');
                await connection.commit();

                // Bağlantıyı serbest bırak
                connection.release();

                // Başarılı yanıt dön
                const responseData = {
                    success: true,
                    message: 'Bakiye başarıyla yüklendi',
                    previousBalance: previousBalance,
                    loadedAmount: requestedAmount,
                    newBalance: newBalance,
                    transactionId: transactionId
                };

                console.log('Yanıt verisi:', responseData);
                return res.status(200).json(responseData);

            } catch (transactionError) {
                // Hata durumunda transaction'ı geri al
                console.error('Transaction hatası, geri alınıyor:', transactionError);
                await connection.rollback();
                connection.release();

                return res.status(500).json({
                    success: false,
                    message: 'Bakiye yükleme işlemi sırasında bir hata oluştu.',
                    error: transactionError.message
                });
            }
        } catch (error) {
            console.error('Bakiye yükleme hatası:', error);
            return res.status(500).json({
                success: false,
                message: 'Bakiye yükleme işlemi sırasında bir hata oluştu.',
                error: error.message
            });
        }
    });

    return router;
};
