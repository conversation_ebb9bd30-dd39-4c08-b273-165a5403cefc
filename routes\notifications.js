const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');

module.exports = (pool) => {
    router.get('/', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            // -- CHECK USER -- //
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }

            const [notifications] = await pool.query('SELECT * FROM notifications WHERE userId = ? ORDER BY createdAt', [userId]);

            return res.status(200).json({ type: 'success', notifications });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    return router;
};
