const express = require('express');
const router = express.Router();

module.exports = (pool) => {
    // Kart detayları ve işlem geçmişi endpoint'i (Frontend için)
    router.get("/card-details/:cardNumber", async (req, res) => {
        try {
            const { cardNumber } = req.params;

            // Gift Card kontrolü (cardNumber, code alanına karşılık geliyor)
            const [cardData] = await pool.query('SELECT * FROM gift_cards WHERE code = ?', [cardNumber]);

            if (cardData.length === 0) {
                return res.status(200).json({
                    type: 'error',
                    error: 'Kart bulunamadı.'
                });
            }
            const card = cardData[0];

            // Kart işlem geçmişini al
            const [rawTransactionsData] = await pool.query(
                `SELECT
                    id,
                    amount,
                    createdAt as date,
                    CASE
                        WHEN amount > 0 THEN 'deposit'
                        ELSE 'payment'
                    END as type,
                    CASE
                        WHEN amount > 0 THEN 'Kart Yükleme'
                        WHEN amount < -40 THEN 'Kahve Siparişi'
                        WHEN amount < -30 THEN 'İçecek Siparişi'
                        WHEN amount < -20 THEN 'Tatlı Siparişi'
                        ELSE 'Ürün Siparişi'
                    END as description
                FROM gift_card_usage_history
                WHERE cardId = ?
                ORDER BY createdAt DESC
                LIMIT 50`,
                [card.id]
            );

            // amount değerini number tipine dönüştür
            const transactionsData = rawTransactionsData.map(transaction => ({
                ...transaction,
                amount: parseFloat(transaction.amount) // String'den number'a dönüştür
            }));



            // Cache kontrolü için headers
            res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
            res.setHeader('Surrogate-Control', 'no-store');

            // Başarılı yanıt dön
            return res.status(200).json({
                type: 'success',
                balance: parseFloat(card.value),
                transactions: transactionsData
            });

        } catch (error) {
            console.error(`Kart detayları getirme hatası: ${error.message}`);
            return res.status(200).json({
                type: 'error',
                error: 'Kart detayları alınırken bir hata oluştu.'
            });
        }
    });
    // Kart bilgilerini getirme endpoint'i
    router.get("/:cardCode", async (req, res) => {
        try {
            const { cardCode } = req.params;

            // Gift Card kontrolü
            const [cardData] = await pool.query('SELECT * FROM gift_cards WHERE code = ?', [cardCode]);

            if (cardData.length === 0) {
                return res.status(200).json({
                    type: 'error',
                    error: 'Kart bulunamadı.'
                });
            }
            const card = cardData[0];

            // Kart durumu kontrolü
            const statusText = card.status === 1 ? 'active' : 'inactive';

            // Son kullanım tarihini al
            const [lastUsageData] = await pool.query(
                'SELECT createdAt FROM gift_card_usage_history WHERE cardId = ? ORDER BY createdAt DESC LIMIT 1',
                [card.id]
            );

            const lastUsed = lastUsageData.length > 0 ? lastUsageData[0].createdAt : null;

            // Kart tipini belirle
            let cardType = "standard";
            if (card.type === 1) cardType = "premium";
            else if (card.type === 2) cardType = "vip";

            // Cache kontrolü için headers
            res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
            res.setHeader('Surrogate-Control', 'no-store');

            // Başarılı yanıt dön
            return res.status(200).json({
                type: 'success',
                data: {
                    id: card.code, // Kart kodu ID olarak kullanılıyor
                    balance: parseFloat(card.value),
                    cardType: cardType,
                    status: statusText,
                    lastUsed: lastUsed,
                    code: card.code
                }
            });

        } catch (error) {
            console.error(`Kart bilgisi getirme hatası: ${error.message}`);
            return res.status(200).json({
                type: 'error',
                error: 'Kart bilgileri alınırken bir hata oluştu.'
            });
        }
    });

    // Karta bakiye yükleme endpoint'i
    router.post("/:cardCode/load", async (req, res) => {
        try {
            const { cardCode } = req.params;
            const { amount } = req.body;
            logger.info(`Karta bakiye yükleme isteği: ${cardCode}, miktar: ${amount}`);

            // Miktar kontrolü
            if (!amount) {
                logger.warn('Miktar belirtilmedi');
                return res.status(200).json({
                    type: 'error',
                    error: 'Yüklenecek miktar belirtilmelidir.'
                });
            }

            const requestedAmount = parseFloat(amount);
            if (isNaN(requestedAmount) || requestedAmount <= 0) {
                logger.warn(`Geçersiz miktar: ${amount}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Geçersiz miktar. Pozitif bir sayı girilmelidir.'
                });
            }

            // Gift Card kontrolü
            const [cardData] = await pool.query('SELECT * FROM gift_cards WHERE code = ?', [cardCode]);

            if (cardData.length === 0) {
                logger.warn(`Kart bulunamadı: ${cardCode}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Kart bulunamadı.'
                });
            }
            const card = cardData[0];

            // Kart sahibi bilgilerini al
            const cardOwnerId = card.userId;
            logger.info(`Kart sahibi ID: ${cardOwnerId}`);

            const [cardOwnerData] = await pool.query('SELECT * FROM users WHERE id = ?', [cardOwnerId]);
            if (cardOwnerData.length === 0) {
                logger.warn(`Kart sahibi bulunamadı: ${cardOwnerId}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Kart sahibi kullanıcı bulunamadı.'
                });
            }

            // Kart durumu kontrolü
            if (card.status !== 1) {
                logger.warn(`Kart aktif değil, durum: ${card.status}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Bu kart aktif değil.'
                });
            }

            // Cache kontrolü için headers
            res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
            res.setHeader('Surrogate-Control', 'no-store');

            // Transaction için ayrı bağlantı al
            const connection = await pool.getConnection();

            try {
                await connection.beginTransaction();

                // Kart bakiyesini güncelle
                const previousBalance = parseFloat(card.value);
                const newBalance = previousBalance + requestedAmount;

                await connection.query('UPDATE gift_cards SET value = ? WHERE id = ?', [newBalance, card.id]);

                // Kullanım geçmişi kaydı oluştur - Kart sahibinin ID'si ile
                const [insertResult] = await connection.query(
                    'INSERT INTO gift_card_usage_history (userId, cardId, amount, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
                    [cardOwnerId, card.id, requestedAmount.toString()]
                );

                const transactionId = "tx" + insertResult.insertId;

                // Transaction'ı tamamla
                await connection.commit();

                // Bağlantıyı serbest bırak
                connection.release();

                // Başarılı yanıt dön
                return res.status(200).json({
                    type: 'success',
                    message: 'Bakiye başarıyla yüklendi',
                    previousBalance: previousBalance,
                    loadedAmount: requestedAmount,
                    newBalance: newBalance,
                    transactionId: transactionId
                });

            } catch (transactionError) {
                // Hata durumunda transaction'ı geri al
                console.error(`Transaction hatası, geri alınıyor: ${transactionError.message}`);
                await connection.rollback();
                connection.release();

                return res.status(200).json({
                    type: 'error',
                    error: 'Bakiye yükleme işlemi sırasında bir hata oluştu.'
                });
            }
        } catch (error) {
            return res.status(200).json({
                type: 'error',
                error: 'Bakiye yükleme işlemi sırasında bir hata oluştu.'
            });
        }
    });

    // Karttan para çekme endpoint'i (Garson uygulaması için)
    router.post("/:cardCode/charge", async (req, res) => {
        try {
            const { cardCode } = req.params;
            const { amount, description } = req.body;
            console.log(`Karttan para çekme isteği: ${cardCode}, miktar: ${amount}, açıklama: ${description}`);

            // Miktar kontrolü
            if (!amount) {
                console.log('Miktar belirtilmedi');
                return res.status(200).json({
                    type: 'error',
                    error: 'Çekilecek miktar belirtilmelidir.'
                });
            }

            const requestedAmount = parseFloat(amount);
            if (isNaN(requestedAmount) || requestedAmount <= 0) {
                console.log(`Geçersiz miktar: ${amount}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Geçersiz miktar. Pozitif bir sayı girilmelidir.'
                });
            }

            // Gift Card kontrolü
            const [cardData] = await pool.query('SELECT * FROM gift_cards WHERE code = ?', [cardCode]);

            if (cardData.length === 0) {
                console.log(`Kart bulunamadı: ${cardCode}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Kart bulunamadı.'
                });
            }
            const card = cardData[0];

            // Kart sahibi bilgilerini al
            const cardOwnerId = card.userId;
            console.log(`Kart sahibi ID: ${cardOwnerId}`);

            const [cardOwnerData] = await pool.query('SELECT * FROM users WHERE id = ?', [cardOwnerId]);
            if (cardOwnerData.length === 0) {
                console.log(`Kart sahibi bulunamadı: ${cardOwnerId}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Kart sahibi kullanıcı bulunamadı.'
                });
            }

            // Kart durumu kontrolü
            if (card.status !== 1) {
                console.log(`Kart aktif değil, durum: ${card.status}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Bu kart aktif değil.'
                });
            }

            // Bakiye kontrolü
            const currentBalance = parseFloat(card.value);
            if (currentBalance < requestedAmount) {
                console.log(`Yetersiz bakiye: ${currentBalance} < ${requestedAmount}`);
                return res.status(200).json({
                    type: 'error',
                    error: 'Yetersiz bakiye.'
                });
            }

            // Cache kontrolü için headers
            res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
            res.setHeader('Surrogate-Control', 'no-store');

            // Transaction için ayrı bağlantı al
            console.log('Transaction başlatılıyor...');
            const connection = await pool.getConnection();

            try {
                await connection.beginTransaction();

                // Kart bakiyesini güncelle
                const newBalance = currentBalance - requestedAmount;
                console.log(`Bakiye güncelleniyor: ${currentBalance} - ${requestedAmount} = ${newBalance}`);

                await connection.query('UPDATE gift_cards SET value = ? WHERE id = ?', [newBalance, card.id]);

                // Kullanım geçmişi kaydı oluştur - Kart sahibinin ID'si ile (negatif miktar)
                console.log('Kullanım geçmişi kaydı oluşturuluyor...');
                const [insertResult] = await connection.query(
                    'INSERT INTO gift_card_usage_history (userId, cardId, amount, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
                    [cardOwnerId, card.id, -requestedAmount.toString()]
                );

                const transactionId = "tx" + insertResult.insertId;
                console.log(`İşlem ID: ${transactionId}`);

                // Transaction'ı tamamla
                console.log('Transaction tamamlanıyor...');
                await connection.commit();

                // Bağlantıyı serbest bırak
                connection.release();

                // Başarılı yanıt dön
                return res.status(200).json({
                    type: 'success',
                    message: 'Ödeme başarıyla alındı',
                    previousBalance: currentBalance,
                    chargedAmount: requestedAmount,
                    newBalance: newBalance,
                    transactionId: transactionId,
                    cardOwner: {
                        id: cardOwnerData[0].id,
                        name: `${cardOwnerData[0].firstName} ${cardOwnerData[0].lastName}`,
                        email: cardOwnerData[0].email,
                        phone: cardOwnerData[0].phoneNumber
                    }
                });

            } catch (transactionError) {
                // Hata durumunda transaction'ı geri al
                console.error(`Transaction hatası, geri alınıyor: ${transactionError.message}`);
                await connection.rollback();
                connection.release();

                return res.status(200).json({
                    type: 'error',
                    error: 'Ödeme işlemi sırasında bir hata oluştu.'
                });
            }
        } catch (error) {
            console.error(`Ödeme hatası: ${error.message}`);
            return res.status(200).json({
                type: 'error',
                error: 'Ödeme işlemi sırasında bir hata oluştu.'
            });
        }
    });

    return router;
};
