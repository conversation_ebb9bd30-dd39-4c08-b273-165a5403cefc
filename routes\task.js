const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');


module.exports = (pool) => {
    router.post('/complete-task',
        [
            body('level').notEmpty(),
            body('task').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const { level, task } = req.body;
            const userId = req.decodedToken.userId;
            try {
                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: '<PERSON><PERSON>ı<PERSON>ı bulunamadı!' });
                }
                const user = users[0];
                let userLevel = JSON.parse(user.level);
                const complete = userLevel[level - 1, task - 1].complete;

                if (complete == false) {
                    userLevel[level - 1, task - 1].value += 1;

                    const target = userLevel[level - 1, task - 1].target;
                    const value = userLevel[level - 1, task - 1].value;

                    if (target >= value) {
                        userLevel[level - 1, task - 1].complete = true;
                    }
                }

                await pool.query(
                    `
                UPDATE users 
                SET level = ?
                WHERE id = ?
                `,
                    [JSON.stringify(userLevel), userId]
                );

                return res.status(200).json({ type: 'success' });
            } catch (err) {
                console.error(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    return router;
};