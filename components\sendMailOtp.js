const nodemailer = require('nodemailer');

async function sendMailOtp(toEmail, code) {
  const transporter = nodemailer.createTransport({
    host: "mail.hollystone.com.tr",
    port: 465,
    secure: true, 
    auth: {
      user: "<EMAIL>",
      pass: "44776108232@ESes"
    }
  });

  const mailOptions = {
    from: '"HollyStone - Şifre Sıfırlama" <<EMAIL>>',
    to: toEmail,
    subject: "Şifre Sıfırlama Kodu",
    html: `<p>Şifre sıfırlama için OTP kodunuz: <strong>${code}</strong></p>`
  };

  return transporter.sendMail(mailOptions);
}

module.exports = sendMailOtp;
