const express = require('express');
const crypto = require('crypto');
const nodeBase64 = require('nodejs-base64-converter');
const axios = require('axios');
const zlib = require('zlib');
const qs = require('querystring');

const merchant_id = '301288';
const merchant_key = 'naFdYPfMh54DtFgk';
const merchant_salt = 'QER98QJtrzssxJ8b';
const currency = 'TL';
const test_mode = '1'; 
const debug_on = 1;
const client_lang = 'tr';
const payment_type = 'card'; 
const non_3d = '0';
const card_type = '';
const installment_count = '0';
const non3d_test_failed = '0';
const no_installment = '0';
const max_installment = '0';
const okUrl = `https://api.hollystone.com.tr/api/payment/callback-success`;
const failUrl = `https://api.hollystone.com.tr/api/payment/callback-fail`;


async function getpaymentWithPayTR(
    user_name,
    user_address,
    user_phone,
    email,
    payment_amount,
    basket,
    user_ip,
    merchant_oid,
) {
    try {
        console.log("📌 PayTR Ödeme İşlemi Başlatıldı...");
        
        const user_basket = nodeBase64.encode(JSON.stringify(basket));

        const hashSTR = `${merchant_id}${user_ip}${merchant_oid}${email}${payment_amount}${user_basket}${no_installment}${max_installment}${currency}${test_mode}`;
        
        
        const paytr_token = hashSTR + merchant_salt;
        const token = crypto.createHmac("sha256", merchant_key).update(paytr_token).digest("base64");

        const requestData = {
            merchant_id,
            merchant_key,
            merchant_salt,
            email,
            payment_amount,  // **🔥 Integer olarak gönderildi (örneğin 10000 = 100 TL)**
            merchant_oid,
            user_name,
            user_address,
            user_phone,
            merchant_ok_url: okUrl,
            merchant_fail_url: failUrl,
            user_basket,
            user_ip,
            debug_on,
            test_mode,
            no_installment,
            max_installment,
            currency,
            paytr_token: token,  // **🔥 Doğru şekilde hesaplandı!**
        };


        console.log("📢 GET-TOKEN İsteği (Adım 1):", requestData);

    // **Adım 1:** Token almak için PayTR’ın get-token endpoint’ine istek gönderiyoruz.
    const response = await axios.post(
      "https://www.paytr.com/odeme/api/get-token",
      qs.stringify(requestData),
      { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
    );
    const res_data = response.data;
    console.log("📢 GET-TOKEN Yanıtı:", res_data);

    if (res_data.status === "failed") {
      console.error("⛔ Ödeme Hatası (Adım 1):", res_data.reason);
      return { type: "error", error: res_data.reason };
    }
    // Örneğin, get-token yanıtında token değeri res_data.token olarak geliyor.
    return { type: "success", token: res_data.token };
  } catch (err) {
    console.error("⛔ API Hatası (Adım 1):", err);
    return { type: "error", error: "Ödeme işlemi sırasında hata oluştu (Adım 1)." };
  }
}
async function processPaymentFlow(paymentParams, cardParams) {
    // Adım 1: Token al
    const step1 = await getpaymentWithPayTR(
      paymentParams.user_name,
      paymentParams.user_address,
      paymentParams.user_phone,
      paymentParams.email,
      paymentParams.payment_amount,
      paymentParams.basket,
      paymentParams.user_ip,
      paymentParams.merchantOid,
      cardParams.card_number,
      cardParams.expiry_month,
      cardParams.expiry_year,
      cardParams.cvv
    );
  
    if (step1.type === "error") {
      return step1;
    }
    const tokenFromStep1 = step1.token;
    console.log("✅ Adım 1 Token Alındı:", tokenFromStep1);
  
    // Adım 2: /odeme endpoint’ine istek gönder
    // NOT: Burada cardParams nesnesini ek parametre olarak gönderiyoruz
    const step2 = await getpayment(
      paymentParams.user_name,
      paymentParams.user_address,
      paymentParams.user_phone,
      paymentParams.email,
      paymentParams.payment_amount,
      paymentParams.basket,
      paymentParams.user_ip,
      cardParams.utoken || "",
      cardParams.ctoken || "",
      paymentParams.merchantOid,
      tokenFromStep1,
      cardParams   // Bu eklenmeli!
    );
    return step2;
  }
  
  async function getpayment(
    user_name,
    user_address,
    user_phone,
    email,
    payment_amount,   // kuruş cinsinden
    basket,           // Örnek: [ [ ürün_adı, fiyat, adet ] ]
    user_ip,
    utoken,         // Kayıtlı kart varsa token, yoksa boş string
    ctoken,         // Aynı şekilde
    merchantOid,
    tokenFromStep1, // Adım 1'den alınan token
    cardDetails     // { card_number, expiry_month, expiry_year, cvv } – kayıtlı değilse gönderilecek
  ) {
    // Sepet bilgisini base64 encode edelim
    const user_basket = nodeBase64.encode(JSON.stringify(basket));
    try {
      // Dokümantasyona göre hash string hesaplaması (bu örnekte farklı bir hash hesaplaması kullanıyoruz)
      const hashSTR = `${merchant_id}${user_ip}${merchantOid}${email}${payment_amount}${payment_type}${installment_count}${currency}${test_mode}${non_3d}`;
      const paytr_token = hashSTR + merchant_salt;
      const tokenForStep2 = crypto.createHmac("sha256", merchant_key)
        .update(paytr_token)
        .digest("base64");
  
      // Temel parametreler
      let requestData = {
        merchant_id,
        user_ip,
        merchant_oid: merchantOid,
        email,
        payment_type,
        payment_amount,
        currency,
        test_mode,
        non_3d,
        merchant_ok_url: okUrl,
        merchant_fail_url: failUrl,
        user_name,
        user_address,
        user_phone,
        user_basket,
        debug_on,
        lang: client_lang,
        paytr_token: tokenForStep2,
        non3d_test_failed,
        installment_count,
        card_type,
        utoken,   // Eğer kayıtlı kart varsa
        ctoken,   // Eğer kayıtlı kart varsa
        token: tokenFromStep1  // Adım 1'den alınan token
      };
  
      if (!utoken || !ctoken) {
        if (!cardDetails) {
          return { type: "error", error: "Kart bilgileri eksik" };
        }
        requestData = {
          ...requestData,
          card_number: cardDetails.card_number,
          expiry_month: cardDetails.expiry_month,
          expiry_year: cardDetails.expiry_year,
          cvv: cardDetails.cvv,
          cc_owner: 'Selim Weez'
        };
      }
      
  
      console.log("📢 /odeme İsteği (Adım 2):", requestData);
  
      // Adım 2: Doğrudan PayTR'ın /odeme endpoint'ine istek gönder
      const response = await axios.post(
        "https://www.paytr.com/odeme",
        qs.stringify(requestData),
        { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
      );
      const res_data = response.data;
      console.log("📢 /odeme Yanıtı (Adım 2):", res_data);
  
      if (res_data.status === "failed") {
        console.error("⛔ Ödeme Hatası (Adım 2):", res_data.reason);
        return { type: "error", error: res_data.reason };
      } else {
        // Eğer PayTR, HTML içeriği döndürüyorsa; bu örnekte form içeriği alınır.
        const compressedHtmlContent = zlib.deflateSync(res_data);
        const encodedHtmlContent = encodeURIComponent(compressedHtmlContent.toString("base64"));
        // Kendi ödeme ekranınızda bu HTML'i gösterebilirsiniz.
        return { type: "success", paymentLink: `https://api.hollystone.com.tr/api/payment/view-html?htmlContent=${encodedHtmlContent}` };
      }
    } catch (err) {
      console.error("⛔ API Hatası (Adım 2):", err);
      return { type: "error", error: "Ödeme işlemi sırasında hata oluştu (Adım 2)." };
    }
  }
  
  

async function getCardList(utoken) {
    return new Promise((resolve, reject) => {
        var paytr_token = crypto.createHmac('sha256', merchant_key).update(utoken + merchant_salt).digest('base64');

        const requestData = {
            merchant_id,
            utoken,
            paytr_token,
        };

        axios.post('https://www.paytr.com/odeme/capi/list', requestData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        })
            .then(response => {
                resolve(response.data);
            })
            .catch(error => {
                reject(error);
            });
    });
}

async function storeCard(user_name, user_address, user_phone, email, user_ip, utoken, merchantOid, cc_owner, card_number, expiry_month, expiry_year, cvv) {
    const payment_amount = '1.00';
    const store_card = '1';
    const basket = JSON.stringify([['Kart Kaydet', '1.00', 1]]);
    const user_basket = nodeBase64.encode(basket);
    try {
        const hashSTR = `${merchant_id}${user_ip}${merchantOid}${email}${payment_amount}${payment_type}${installment_count}${currency}${test_mode}${non_3d}`;
        const paytr_token = hashSTR + merchant_salt;
        const token = crypto.createHmac('sha256', merchant_key).update(paytr_token).digest('base64');

        const requestData = {
            merchant_id,
            user_ip,
            merchant_oid: merchantOid,
            email,
            payment_type,
            payment_amount,
            currency,
            test_mode,
            non_3d,
            merchant_ok_url: okUrl,
            merchant_fail_url: failUrl,
            user_name,
            user_address,
            user_phone,
            user_basket,
            debug_on,
            client_lang,
            paytr_token: token,
            non3d_test_failed,
            installment_count,
            card_type,
            utoken,
            store_card,
            cc_owner,
            card_number,
            expiry_month,
            expiry_year,
            cvv,
        };

        const response = await axios.post('https://www.paytr.com/odeme', requestData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        });

        const res_data = response.data;

        if (res_data.status == "failed") {
            console.error({ type: 'error', error: res_data.reason });
            return { type: 'error', error: res_data.reason };
        } else {
            const compressedHtmlContent = zlib.deflateSync(res_data);
            const encodedHtmlContent = encodeURIComponent(compressedHtmlContent.toString('base64'));
            return { type: 'success', paymentLink: `https://api.hollystone.com.tr/api/payment/view-html?htmlContent=${encodedHtmlContent}` };
        }

    }
    catch (err) {
        console.error(err);
        return {
            type: 'error',
            error: 'Internal Server Error'
        };
    }
}

async function deleteCard(utoken, ctoken) {
    return new Promise((resolve, reject) => {
        var paytr_token = crypto.createHmac('sha256', merchant_key).update(ctoken + utoken + merchant_salt).digest('base64');

        const requestData = {
            merchant_id,
            utoken,
            ctoken,
            paytr_token
        };

        axios.post('https://www.paytr.com/odeme/capi/delete', requestData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        })
            .then(response => {
                resolve(response.data);
            })
            .catch(error => {
                reject(error);
            });
    });
}

async function refund(returnAmount, merchantOid) {
    return new Promise((resolve, reject) => {
        const hashSTR = `${merchant_id}${merchantOid}${returnAmount}`;
        const paytr_token = hashSTR + merchant_salt;
        const token = crypto.createHmac('sha256', merchant_key).update(paytr_token).digest('base64');

        const requestData = {
            merchant_id,
            merchant_oid: merchantOid,
            return_amount: returnAmount,
            paytr_token: token,
            test_mode,
        };

        axios.post('https://www.paytr.com/odeme/iade', requestData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        })
            .then(response => {
                resolve(response.data);
            })
            .catch(error => {
                reject(error);
            });
    });
}

async function transferPayment(returnAmount, submerchantAmount, merchantOid, transId, transferName, transferIban) {
    return new Promise((resolve, reject) => {
        const hash_str = merchant_id + merchantOid + transId + submerchantAmount + returnAmount + transferName + transferIban;
        const paytr_token = crypto.createHmac('sha256', merchant_key).update(hash_str + merchant_salt).digest('base64');

        const requestData = {
            merchant_id,
            merchant_oid: merchantOid,
            trans_id: transId,
            submerchant_amount: submerchantAmount,
            total_amount: returnAmount,
            transfer_name: transferName,
            transfer_iban: transferIban,
            paytr_token,
            test_mode,
        };

        axios.post('https://www.paytr.com/odeme/platform/transfer', requestData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        })
            .then(response => {
                resolve(response.data);
            })
            .catch(error => {
                reject(error);
            });
    });
}

function getPaymentInfo(merchantOid) {
    return new Promise((resolve, reject) => {
        const paytr_token = crypto.createHmac('sha256', merchant_key).update(merchant_id + merchantOid + merchant_salt).digest('base64');

        const requestData = {
            merchant_id,
            merchant_oid: merchantOid,
            paytr_token,
        }

        axios.post('https://www.paytr.com/odeme/durum-sorgu', requestData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        })
            .then(response => {
                resolve(response.data);
            })
            .catch(error => {
                reject(error);
            });
    });
}

async function tokenGenerator(callbackMerchantOid, callbackStatus, callbacTotalAmount) {
    const paytr_token = callbackMerchantOid + merchant_salt + callbackStatus + callbacTotalAmount;
    const token = crypto.createHmac('sha256', merchant_key).update(paytr_token).digest('base64');

    return token;
}

module.exports = {
    getpayment,
    getPaymentInfo,
    storeCard,
    deleteCard,
    tokenGenerator,
    getCardList,
    refund,
    processPaymentFlow,
    getpaymentWithPayTR,
    transferPayment
};